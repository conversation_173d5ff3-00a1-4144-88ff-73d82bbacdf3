<?php

namespace App\Traits;

use App\Jobs\CheckCapability;
use App\Models\AgentContactRelationModel;
use App\Models\AgentDeviceRelationModel;
use App\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use App\Models\Device;
use App\Models\Template;
use App\Models\Smstransaction;
use App\Models\Task;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rules\Unique;
use Illuminate\Database\Query\JoinClause;
use Carbon\Carbon;

trait Whatsapp
{
    private function sendwhatsapp($data, $masking)
    {
        // $components = [];

        // $data['templateId']
        Log::info('data is ' . json_encode($data));
        // Log::debug('template id is ', $data['templateId']);
        // dd('hello');
        // if ($data['parameters']) {
        //     $parameters = explode("||", $data['parameters']);
        //     $components[] = [
        //         'type' => 'body',
        //         'parameters' => array_map(function ($item) {
        //             return ['type' => 'text', 'text' => $item];
        //         }, $parameters),
        //     ];
        // }

        // if ($data['task_url']) {
        //     $component = [
        //         'type' => 'header',
        //         'parameters' => [
        //             [
        //                 'type' => $data['task_type'],
        //                 $data['task_type'] => [
        //                     'link' => $data['task_url'],
        //                 ],
        //             ],
        //         ],
        //     ];

        //     if ($data['task_type'] === 'document') {
        //         $component['parameters'][0][$data['task_type']]['filename'] = $data['campaign_name'] ?? 'document'; // Replace 'document' with the actual filename
        //     }
        //     $components[] = $component;
        // }

        // if ($data['flow_id']) {
        //     $flow_data = '[ {
        //         "type": "button",
        //         "sub_type": "flow",
        //         "index": "0",
        //          "parameters": [
        //   {
        //     "type": "action",
        //     "action": {
        //      "flow_token": ' . $data['flow_id'] . '
        //     }
        //   }
        // ]
        //     }]';
        //     $flow_json = json_decode($flow_data, true);
        //     $components = array_merge($components, $flow_json);
        // }

        // if ($data['buttons']) {
        //     $buttons = json_decode($data['buttons'], true);
        //     $components = array_merge($components, $buttons);
        // }

        // if (empty($components)) {
        //     $components = null;
        // }

        $device = Device::where('phone', $data['phone'])->first();

        $template = Template::where('id', $data['templateId'])->first();

        $messageData = [];

        if ($data['is_reply'] == "1" || $data['is_reply'] == 1) {
            $messageData = [
                'messageID' => $data['whatsapp_id'],
                'agentID' => $device->token,
                'contacts' => [
                    $data['send_to_number']
                ],
                'data' => [
                    'content' => [
                        'plainText' => $data['text']
                    ]
                ],
                'data_sms' => [
                    "sender_id" => "asdf",
                    "domain_id" => "Add Domain ID",
                    "sms_type" => "T",
                    "sms_content_type" => "Static",
                    "dlt_entity_id" => "12044854436650",
                    "body" => "SMS Body",
                    "dlt_template_id" => "120717281037"
                ]
            ];
        } else {
            $messageData = [
                'messageID' => $data['whatsapp_id'],
                'agentID' => $device->token,
                // 'campaignID' => 'test',
                'contacts' => [
                    $data['send_to_number']
                ],
                'data' => $template->body,
                'data_sms' => [
                    "sender_id" => "asdf",
                    "domain_id" => "Add Domain ID",
                    "sms_type" => "T",
                    "sms_content_type" => "Static",
                    "dlt_entity_id" => "12044854436650",
                    "body" => "SMS Body",
                    "dlt_template_id" => "120717281037"
                ]
            ];
        }

        $task = Task::where('task_id', $data['task_id'])->first();
        $mobile = $task->send_to_number;
        $mobileNumber = $mobile;
        if ($masking == 1) {
            $length = strlen($mobile);
            if ($length > 4) {
                $mobileNumber = substr($mobile, 0, $length - 4) . "****";
                // Log::info("masking is . $mobileNumber");
            } else {
                // Log::info("masking is not done short number");
                $mobileNumber = $mobile;
            }
        }

        $user = User::where('id', $device->user_id)->first();

        // $apiKey = env('RCS_API_KEY');
        // if api key not found on env then not send message
        $apiKey = env('RCS_API_KEY');
        if (empty($apiKey)) {
            Log::info('RCS API key not found in env');

            $this->updateTask($data['task_id'], null, 4, 'RCS API key not found', $mobileNumber);

            $balance = $user->business_initiated * 1;
            $user->balance += $balance;
            $user->save();

            Log::info('Balance added to user');

            return false;
        }
        // dd($apiKey);
        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'x-apikey' => $apiKey,
            ])->post('https://rcsapi.jiocx.com/api/v1/sendMessage', $messageData);

            Log::debug($response);
            //$url = "https://rcs.telinfy.com/rcsapi/send/{$data['send_to_number']}?messageId={$data['whatsapp_id']}";
            // Log::debug('Message Data: ' . print_r($messageData, true));
            Log::debug('Message Data: ' . json_encode($messageData));
            //Log::info('URL is:', [$url]);
            //Log::info('Token is:', [$data['token']]);

            // dd('rcs response');
            // Log::debug('RCS API Response', [
            //     'status'   => $response->status(),
            //     'body'     => $response->body(),
            //     'json'     => $response->json()
            // ]);
            //  return json_decode($response->body());
            $rcsResponse = $response->json();

            if ($rcsResponse['message'] == 'OK') {
                Log::info('Message send okay with' . $rcsResponse['referenceID']);

                $this->updateTask($data['task_id'], (string) $response, 1, null, $mobileNumber);
            } else {
                // Optionally, handle error here or throw an exception
                Log::error('RCS API error', ['response' => $response->body()]);

                $this->updateTask($data['task_id'], null, 4, $response->body(), $mobileNumber);

                $balance = $user->business_initiated * 1;
                $user->balance += $balance;
                $user->save();

                return response()->json(['error' => 'Failed to send template.'], $response->status());
            }
        } catch (\Exception $e) {
            // Handle the exception, e.g., log the error
            Log::error($e->getMessage(), [
                'stacktrace' => $e->getTraceAsString(),
                // 'request' => $response->body(),
                'request' => Request::capture()->getContent(),
            ]);
            // $desc = json_decode($response->body())->error->error_data->details ?? null;
            $this->updateTask($data['task_id'], null, 4, $e->getMessage(), $mobileNumber);

            $balance = $user->business_initiated * 1;
            $user->balance += $balance;
            $user->save();
        }
    }

    private function messageSend($data, $from, $reciver, $type, $filter = false, $delay = 0)
    {
        $delay = $delay == 0 ? env('DELAY_TIME', 1000) : $delay;

        if ($delay < 500) {
            $delay = 1;
        } else {
            $delay = $delay / 1000;
            $delay = round($delay);
        }

        sleep($delay);

        $device = Device::where('status', 1)->where('id', $from)->first();
        if (empty($device)) {
            return false;
        }

        //creating session id
        $session_id = 'device_' . $from;

        //formating message
        $message = $this->formatBody($device->user_id, $data['message'] ?? '');

        //formating array context
        $formatedBody = $filter == false ? $this->formatArray($data, $message, $type) : $data;

        //get server url
        $whatsServer = env('WA_SERVER_URL');

        //formating array before sending data to server
        $body['receiver'] = $reciver;
        $body['delay'] = 0;
        $body['message'] = $formatedBody;

        //sending data to whatsapp server
        try {
            $response = Http::post($whatsServer . '/chats/send?id=' . $session_id, $body);
            $status = $response->status();

            if ($status != 200) {
                $responseBody = json_decode($response->body());
                $responseData['message'] = $responseBody->message;
                $responseData['status'] = $status;
            } else {
                $responseData['status'] = 200;
            }

            return $responseData;
        } catch (\Exception $e) {
            $responseData['status'] = 403;
            return $responseData;
        }
    }

    private function getChats($device_phone, $device_id, $msg_status, $tag, $days, $contsearch, $lasttimestamp = null, $perPage = 20)
    {
        // $session_id = 'device_' . $device_id;
        // $whatsServer = env('WA_SERVER_URL');

        // $response = Http::get($whatsServer . '/chats?id=' . $session_id);
        // $status = $response->status();
        //DB::enableQueryLog();
        if (Auth::user()->role == 'user') {
            if ($lasttimestamp != '') {
                $date = $lasttimestamp;
            } else {
                $date = Carbon::now()->subDays($days)->toDateTimeString();
            }
            $userId = Auth::id();

            $contsearch = trim($contsearch);
            // dd($date);
            $query1 = DB::table('messages')
                ->select('messages.sender_no', 'contacts.name', 'messages.timestamp', DB::raw('SUM(CASE WHEN messages.read = 0 THEN 1 ELSE 0 END) AS unreadCount'), 'tags.id as tag_id', 'tags.tag_name', 'tags.color_code')
                //  ->join('contacts', 'contacts.phone', '=', 'messages.sender_no')
                ->leftJoin('contacts', function (JoinClause $join) use ($userId) {
                    $join->on('contacts.phone', '=', 'messages.sender_no')
                        ->where('contacts.user_id', '=', $userId);
                })
                ->leftJoin('tags', 'contacts.tag_id', '=', 'tags.id')
                ->where('messages.device_phone', $device_phone)
                // ->where('contacts.user_id', '=', $userId) // replace $userId with the actual user ID
                ->where('messages.timestamp', '>=', $date)

                ->when($msg_status == 'unread', function ($query) use ($msg_status) {
                    //unread count greater than 0
                    return $query->havingRaw('SUM(CASE WHEN messages.read = 0 THEN 1 ELSE 0 END) > 0');
                })
                ->when($tag != 'all', function ($query) use ($tag) {
                    return $query->where('tags.id', $tag);
                })
                ->groupBy('messages.sender_no', 'contacts.name', 'messages.timestamp', 'tags.id', 'tags.tag_name', 'tags.color_code');

            // ->get();
            // dd($device_phone, $query1->toSql(), $query1->getBindings());

            $query2 = DB::table('task')
                ->select('task.send_to_number as sender_no', 'contacts.name', 'task.scheduled_on as timestamp', DB::raw('0 as unreadCount'), 'tags.id as tag_id', 'tags.tag_name', 'tags.color_code')
                //  ->join('contacts', 'contacts.phone', '=', 'task.send_to_number')
                ->leftJoin('contacts', function (JoinClause $join) use ($userId) {
                    $join->on('contacts.phone', '=', 'task.send_to_number')
                        ->where('contacts.user_id', '=', $userId);
                })
                ->leftJoin('tags', 'contacts.tag_id', '=', 'tags.id')
                ->where('task.device_id', $device_id)

                // ->where('contacts.user_id', '=', $userId)
                ->where('task.launched_on', '>=', $date)
                ->groupBy('task.send_to_number', 'contacts.name', 'task.scheduled_on', 'tags.id', 'tags.tag_name', 'tags.color_code');

            // dd($device_phone, $query2->toSql(), $query2->getBindings());
            if ($msg_status == 'unread' || $tag != 'all') {
                $query2->where(DB::raw('FALSE'));
            }

            if (!empty($contsearch)) {

                // Prepare the input for the LIKE query
                // $contsearch = '%'.$contsearch.'%';

                // $query1->where('messages.sender_no','LIKE',$contsearch);
                // $query2->where('task.send_to_number','LIKE',$contsearch);
                $query1->where(function ($query) use ($contsearch) {
                    $query->whereRaw("LOWER(messages.sender_no) LIKE ?", ["%$contsearch%"])
                        ->orWhereRaw("LOWER(contacts.name) LIKE ?", ["%$contsearch%"]);
                });

                $query2->where(function ($query) use ($contsearch) {
                    $query->whereRaw("LOWER(task.send_to_number) LIKE ?", ["%$contsearch%"])
                        ->orWhereRaw("LOWER(contacts.name) LIKE ?", ["%$contsearch%"]);
                });
            }

            // blacklisted numbers
            $device = Device::where('phone', $device_phone)->first();
            // dd($device->id);
            $getblockUser = getBlockUsers($device->phoneid, $device->token);
            // dd($getblockUser);
            if ($getblockUser['success']) {
                $blacklisted = collect($getblockUser['data'])->pluck('wa_id')->toArray();
            } else {
                $blacklisted = [];
            }
            // dd($blacklisted);

            //unreadcount if problem then try MAX(unreadCount)
            $allUsers = DB::query()->fromSub($query1->unionAll($query2), 'sub')
                ->orderBy('timestamp', 'desc')
                ->select('sender_no', 'name', DB::raw('MAX(timestamp) as timestamp'), DB::raw('SUM(unreadCount) as unreadCount'), 'tag_id', 'tag_name', 'color_code')
                ->groupBy('sender_no', 'name', 'tag_id', 'tag_name', 'color_code')
                ->orderBy('timestamp', 'desc')
                ->paginate($perPage);

            foreach ($allUsers as $user) {
                // Add 'blacklisted' status based on whether the sender_no is in the blacklist
                $user->is_blacklisted = in_array($user->sender_no, $blacklisted);
            }

            $responseData['status'] = 200;
            $responseData['data'] = $allUsers;
            // dd($responseData);

            return $responseData;
        } elseif (Auth::user()->role == 'agent') {
            // agent role data
            // dd($device_phone);
            $device = Device::where('phone', $device_phone)->first();
            // dd($device->id);
            $agentDeviceRelation = AgentDeviceRelationModel::where('agent_id', Auth::user()->id)->where('device_id', $device->id)->first();
            $chatType = $agentDeviceRelation->chat_type;
            // dd($chatType);


            $agentChatAssigned = AgentContactRelationModel::where('agent_id', Auth::user()->id)->pluck('contact_id')->toArray();
            // dd($agentChatAssigned);

            $contactPhoneNum = Contact::whereIn('id', $agentChatAssigned)->pluck('phone')->toArray();
            // dd($contactPhoneNum);
            // $devicePhoneNum=Device::whereIn('id',$agentChatAssigned)->pluck('phone')->toArray();
            // dd($devicePhoneNum);
            if ($lasttimestamp != '') {
                $date = $lasttimestamp;
            } else {
                $date = Carbon::now()->subDays($days)->toDateTimeString();
            }
            $userId = Auth::id();
            $parentId = Auth::user()->parent_id;
            // dd($parentId);

            $contsearch = trim($contsearch);
            // dd($date);
            $query1 = DB::table('messages')
                ->select('messages.sender_no', 'contacts.name', 'messages.timestamp', DB::raw('SUM(CASE WHEN messages.read = 0 THEN 1 ELSE 0 END) AS unreadCount'), 'tags.id as tag_id', 'tags.tag_name', 'tags.color_code')
                //  ->join('contacts', 'contacts.phone', '=', 'messages.sender_no')
                ->leftJoin('contacts', function (JoinClause $join) use ($parentId) {
                    $join->on('contacts.phone', '=', 'messages.sender_no')
                        ->where('contacts.user_id', '=', $parentId);
                })
                ->leftJoin('tags', 'contacts.tag_id', '=', 'tags.id')
                ->where('messages.device_phone', $device_phone)
                // ->where('contacts.user_id', '=', $userId) // replace $userId with the actual user ID
                ->where('messages.timestamp', '>=', $date)

                ->when($msg_status == 'unread', function ($query) use ($msg_status) {
                    //unread count greater than 0
                    return $query->havingRaw('SUM(CASE WHEN messages.read = 0 THEN 1 ELSE 0 END) > 0');
                })
                ->when($tag != 'all', function ($query) use ($tag) {
                    return $query->where('tags.id', $tag);
                })
                ->groupBy('messages.sender_no', 'contacts.name', 'messages.timestamp', 'tags.id', 'tags.tag_name', 'tags.color_code');
            // dd($query1->get());

            // ->get();
            // dd($device_phone, $query1->toSql(), $query1->getBindings());

            $query2 = DB::table('task')
                ->select('task.send_to_number as sender_no', 'contacts.name', 'task.scheduled_on as timestamp', DB::raw('0 as unreadCount'), 'tags.id as tag_id', 'tags.tag_name', 'tags.color_code')
                //  ->join('contacts', 'contacts.phone', '=', 'task.send_to_number')
                ->leftJoin('contacts', function (JoinClause $join) use ($parentId) {
                    $join->on('contacts.phone', '=', 'task.send_to_number')
                        ->where('contacts.user_id', '=', $parentId);
                })
                ->leftJoin('tags', 'contacts.tag_id', '=', 'tags.id')
                ->where('task.device_id', $device_id)

                // ->where('contacts.user_id', '=', $userId)
                ->where('task.launched_on', '>=', $date)
                ->groupBy('task.send_to_number', 'contacts.name', 'task.scheduled_on', 'tags.id', 'tags.tag_name', 'tags.color_code');

            // dd($device_phone, $query2->toSql(), $query2->getBindings());
            if ($msg_status == 'unread' || $tag != 'all') {
                $query2->where(DB::raw('FALSE'));
            }

            if ($chatType == 2) {
                $query1 = $query1->whereIn('messages.sender_no', $contactPhoneNum);
                $query2 = $query2->whereIn('task.send_to_number', $contactPhoneNum);
            }

            if (!empty($contsearch)) {

                // Prepare the input for the LIKE query
                // $contsearch = '%'.$contsearch.'%';

                // $query1->where('messages.sender_no','LIKE',$contsearch);
                // $query2->where('task.send_to_number','LIKE',$contsearch);
                $query1->where(function ($query) use ($contsearch) {
                    $query->whereRaw("LOWER(messages.sender_no) LIKE ?", ["%$contsearch%"])
                        ->orWhereRaw("LOWER(contacts.name) LIKE ?", ["%$contsearch%"]);
                });

                $query2->where(function ($query) use ($contsearch) {
                    $query->whereRaw("LOWER(task.send_to_number) LIKE ?", ["%$contsearch%"])
                        ->orWhereRaw("LOWER(contacts.name) LIKE ?", ["%$contsearch%"]);
                });
            }

            // blacklisted numbers
            $device = Device::where('phone', $device_phone)->first();
            // dd($device->id);
            $getblockUser = getBlockUsers($device->phoneid, $device->token);
            // dd($getblockUser);
            if ($getblockUser['success']) {
                $blacklisted = collect($getblockUser['data'])->pluck('wa_id')->toArray();
            } else {
                $blacklisted = [];
            }
            // dd($blacklisted);

            //unreadcount if problem then try MAX(unreadCount)
            $allUsers = DB::query()->fromSub($query1->unionAll($query2), 'sub')
                ->orderBy('timestamp', 'desc')
                ->select('sender_no', 'name', DB::raw('MAX(timestamp) as timestamp'), DB::raw('SUM(unreadCount) as unreadCount'), 'tag_id', 'tag_name', 'color_code')
                ->groupBy('sender_no', 'name', 'tag_id', 'tag_name', 'color_code')
                ->orderBy('timestamp', 'desc')
                ->paginate($perPage);

            foreach ($allUsers as $user) {
                // Add 'blacklisted' status based on whether the sender_no is in the blacklist
                $user->is_blacklisted = in_array($user->sender_no, $blacklisted);
            }

            $responseData['status'] = 200;
            $responseData['data'] = $allUsers;
            // dd($allUsers);

            return $responseData;
        }
    }



    private function formatArray($data, $message, $type)
    {
        if ($type == 'plain-text') {
            $content['text'] = $message;
        } elseif ($type == 'text-with-media') {
            $content['caption'] = $message;
            $explode = explode('.', $data['attachment']);
            $file_type = strtolower(end($explode));
            $extentions = [
                'jpg' => 'image',
                'jpeg' => 'image',
                'png' => 'image',
                'webp' => 'image',
                'pdf' => 'document',
                'docx' => 'document',
                'xlsx' => 'document',
                'csv' => 'document',
                'txt' => 'document'
            ];

            $content[$extentions[$file_type]] = ['url' => asset($data['attachment'])];
        } elseif ($type == 'text-with-button') {
            $buttons = [];
            foreach ($data['buttons'] as $key => $button) {
                $button_content['buttonId'] = 'id' . $key;
                $button_content['buttonText'] = array('displayText' => $button);
                $button_content['type'] = 1;

                array_push($buttons, $button_content);
            }

            $content['text'] = $message;
            $content['footer'] = $data['footer_text'];
            $content['buttons'] = $buttons;
            $content['headerType'] = 1;
        } elseif ($type == 'text-with-template') {
            $templateButtons = [];
            foreach ($data['buttons'] as $key => $button) {
                $button_type = '';
                $button_action_content = '';

                if ($button['type'] == 'urlButton') {
                    $button_type = 'url';
                    $button_action_content = $button['action'];
                } elseif ($button['type'] == 'callButton') {
                    $button_type = 'phoneNumber';
                    $button_action_content = $button['action'];
                } else {
                    $button_type = 'id';
                    $button_action_content = 'action-id-' . $key;
                }

                $button_actions = [];
                $button_actions['displayText'] = $button['displaytext'];
                $button_actions[$button_type] = $button_action_content;



                $button_context['index'] = $key;
                $button_context[$button['type']] = $button_actions;

                array_push($templateButtons, $button_context);
                $button_context = null;
            }


            $content['text'] = $message;
            $content['footer'] = $data['footer_text'];
            $content['templateButtons'] = $templateButtons;
        } elseif ($type == 'text-with-location') {
            $content['location'] = array(
                'degreesLatitude' => $data['degreesLatitude'],
                'degreesLongitude' => $data['degreesLongitude']
            );
        } elseif ($type == 'text-with-vcard') {
            $vcard = 'BEGIN:VCARD\n' // metadata of the contact card
                . 'VERSION:3.0\n'
                . 'FN:' . $data['full_name'] . '\n' // full name
                . 'ORG:' . $data['org_name'] . ';\n' // the organization of the contact
                . 'TEL;type=CELL;type=VOICE;waid=' . $data['contact_number'] . ':' . $data['wa_number'] . '\n' // WhatsApp ID + phone number
                . 'END:VCARD';


            $content = [
                "contacts" => [
                    "displayName" => "maruf",
                    "contacts" => [[$vcard]]
                ]
            ];
        } elseif ($type == 'text-with-list') {

            $templateButtons = [];

            foreach ($data['section'] as $section_key => $sections) {

                $rows = [];

                foreach ($sections['value'] as $value_key => $value) {

                    $rowArr['title'] = $value['title'];
                    $rowArr['rowId'] = 'option-' . $section_key . '-' . $value_key;

                    if ($value['description'] != null) {
                        $rowArr['description'] = $value['description'];
                    }
                    array_push($rows, $rowArr);
                    $rowArr = [];
                }

                $row['title'] = $sections['title'];
                $row['rows'] = $rows;


                array_push($templateButtons, $row);
                $row = [];
            }

            $content = [
                "text" => $message,
                "footer" => $data['footer_text'],
                "title" => $data['header_title'],
                "buttonText" => $data['button_text'],
                "sections" => $templateButtons
            ];
        }


        return $content;
    }

    private function saveTemplate($data, $message, $type, $user_id, $template_id = null)
    {
        if ($template_id == null) {
            $template = new Template;
        } else {
            $template = Template::findorFail($template_id);
            $template->status = isset($data['status']) ? 1 : 0;
        }

        $template->title = $data['template_name'];
        $template->user_id = $user_id;
        $template->body = $this->formatArray($data, $message, $type);
        $template->type = $type;
        $template->save();

        return true;
    }

    private function saveFile(Request $request, $input)
    {
        $file = $request->file($input);
        $ext = $file->extension();
        $filename = now()->timestamp . '.' . $ext;

        $path = 'uploads/message/' . \Auth::id() . date('/y') . '/' . date('m') . '/';
        $filePath = $path . $filename;


        Storage::put($filePath, file_get_contents($file));

        return Storage::url($filePath);
    }

    private function formatBody($user_id, $context = '')
    {
        if ($context == '') {
            return $context;
        }

        $user = User::where('id', $user_id)->first();

        if (empty($user)) {
            return $context;
        } else {
            return $context;
        }
    }

    private function groupMetaData($group_id, $device_id)
    {
        $whatsServer = env('WA_SERVER_URL');
        $device_id = 'device_' . $device_id;
        $url = $whatsServer . '/groups/meta/' . $group_id . '?id=' . $device_id;

        try {

            $response = Http::get($url);
            $status = $response->status();

            if ($status != 200) {
                $responseBody = json_decode($response->body());
                $responseData['message'] = $responseBody->message;
                $responseData['status'] = $status;
            } else {
                $responseData['status'] = 200;
                $responseData['data'] = json_decode($response->body());
            }

            return $responseData;
        } catch (\Exception $e) {
            $responseData['status'] = 403;
            return $responseData;
        }
    }

    private function template_resolver($templates, $template_id)
    {
        $templates;
    }

    private function formatText($context = '', $contact_data = null, $senderdata = null)
    {
        if ($context == '') {
            return $context;
        }
        if ($contact_data != null) {
            $name = $contact_data['name'] ?? '';
            $phone = $contact_data['phone'] ?? '';

            $context = str_replace('{name}', $name, $context);
            $context = str_replace('{phone_number}', $phone, $context);
        }

        if ($senderdata != null) {
            $sender_name = $senderdata['name'] ?? '';
            $sender_phone = $senderdata['phone'] ?? '';
            $sender_email = $senderdata['email'] ?? '';

            $context = str_replace('{my_name}', $sender_name, $context);
            $context = str_replace('{my_contact_number}', $sender_phone, $context);
            $context = str_replace('{my_email}', $sender_email, $context);
        }

        return $context;
    }

    private function formatCustomText($context = '', $replaceableData = [])
    {
        $filteredContent = $context;

        foreach ($replaceableData ?? [] as $key => $value) {
            $filteredContent = str_replace($key, $value, $filteredContent);
        }

        return $filteredContent;
    }

    private function variablemerge($variable = [])
    {

        foreach ($replaceableData ?? [] as $key => $value) {
        }
    }

    private function saveLog($data)
    {
        $log = new Smstransaction;
        $log->user_id = $data['user_id'] ?? null;
        $log->device_id = $data['device_id'] ?? null;
        $log->app_id = $data['app_id'] ?? null;
        $log->from = $data['from'] ?? null;
        $log->to = $data['to'] ?? null;
        $log->template_id = $data['template_id'] ?? null;
        $log->type = $data['type'] ?? null;
        $log->save();
    }

    private function updateTask($task_id, $whatsapp_id, $status, $description, $send_to_number = null)
    {
        DB::statement('CALL update_task(?, ?, ?, ?, ?)', [
            $task_id,
            $whatsapp_id,
            $status,
            $description,
            $send_to_number
        ]);
    }

    private function savemedia($user_device, $media_id)
    {
        $user = DB::table('devices')
            ->select('phoneid', 'waid', 'token')
            ->where('phone', $user_device)
            ->first();

        if (!$user) {
            // Handle user not found
            return null;
        }

        $phoneid = $user->phoneid;
        $token = $user->token;

        $version = env('FACEBOOK_VERSION', 'v18.0');
        // Construct the URL
        $url = "https://graph.facebook.com/v18.0/{$media_id}?phone_number_id={$phoneid}";

        // Send an HTTP GET request using Laravel's HTTP client
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->get($url);

        if ($response->failed()) {
            // Handle request failure
            return null;
        }

        $mediaData = $response->json();

        $media_url = $mediaData['url'];
        $mime_type = $mediaData['mime_type'];
        $media_size = $mediaData['file_size'];
        //byte to mb
        $media_size = $media_size / 1024 / 1024;
        if ($media_size > 15) {
            return 'media too large:' . $media_size . ' KB';
        } else {

            // Define file extensions based on mime type
            $extensions = [
                'image/jpeg' => '.jpg',
                'image/png' => '.png',
                'image/gif' => '.gif',
                'video/mp4' => '.mp4',
                'audio/mpeg' => '.mp3',
                // WhatsApp-specific MIME types
                'image/webp' => '.webp',
                'audio/ogg' => '.ogg',
                'video/3gpp' => '.3gp',
                'application/pdf' => '.pdf',
                'application/msword' => '.doc',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => '.docx',
                'application/vnd.ms-powerpoint' => '.ppt',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation' => '.pptx',
                'application/vnd.ms-excel' => '.xls',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => '.xlsx',
                'text/vcard' => '.vcf',
                'text/csv' => '.csv',
            ];

            // Determine the file extension
            $ext = $extensions[$mime_type] ?? '.txt';

            // Download and save the media file
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Connection' => 'keep-alive',
            ])->get($media_url);

            if ($response->failed()) {
                // Handle file download failure
                Log::error('Failed to download media file', [
                    'media_id' => $media_id,
                    'response' => $response->body(),
                ]);
                return $response->body();
            }

            $filename = $user_device . '_' . $media_id . $ext;
            $filePath = 'uploads/incomming_media/' . $filename;

            // Store the file using Laravel's Storage
            try {
                Storage::put($filePath, $response->body());
            } catch (\Exception $e) {
                // Handle file storage failure
                Log::error('Failed to store media file', [
                    'media_id' => $media_id,
                    'filename' => $filename,
                    'response' => $response->body(),
                ]);
                return $response->body();
            }

            return $filename;
        }
    }

    private function waba_json_generate($text, $media_url = null, $buttons = null, $list = null, $filename = null)
    {
        if ($buttons || $list) {
            $type = 'interactive';
        } else  if ($media_url) {
            // decide based on url extension
            $ext = pathinfo($media_url, PATHINFO_EXTENSION);
            $ext = strtolower($ext);
            //audio, document, image, sticker, or video
            if ($ext == 'jpg' || $ext == 'jpeg' || $ext == 'png' || $ext == 'gif' || $ext == 'webp') {
                $type = 'image';
            } elseif ($ext == 'mp4' || $ext == '3gp') {
                $type = 'video';
            } elseif ($ext == 'mp3' || $ext == 'ogg' || $ext == 'wav') {
                $type = 'audio';
            } elseif ($ext == 'pdf' || $ext == 'doc' || $ext == 'docx' || $ext == 'ppt' || $ext == 'pptx' || $ext == 'xls' || $ext == 'xlsx' || $ext == 'csv' || $ext == 'vcf') {
                $type = 'document';
            }
        } else {
            $type = 'text';
        }

        if ($type == 'text') {
            $components = [
                'preview_url' => true,
                'body' => $text['body'] ?? $text,
            ];
        }
        if ($type == 'image' || $type == 'video' || $type == 'audio' || $type == 'document') {
            //get file name from url
            $file_name = basename($media_url);
            $components = ['link' => $media_url];
            if ($text) {
                $components['caption'] = $text;
            }
            if ($type == 'document') {
                $components['filename'] = $filename;
            }
        }
        if ($type == 'interactive') {
            // if type button then go to if condition or list then else condition
            if (isset($buttons['btn_type']) && $buttons['btn_type'] == 'custom_button' && !empty($buttons['value'])) {
                $components = [
                    'type' => 'button',
                    'body' => [
                        'text' => $text,
                    ],
                    'action' => [
                        'buttons' => [],
                    ],
                ];

                foreach ($buttons['value'] as $button) {
                    $components['action']['buttons'][] = [
                        'type' => 'reply',
                        'reply' => [
                            'id' => $button['id'],
                            'title' => $button['text'],
                        ],
                    ];
                }
            } else {
                $components = [
                    'type' => 'list',
                    'body' => [
                        'text' => $text,
                    ],
                    'action' => [
                        'button' => 'Select Option',
                        'sections' => isset($list['action']['sections']) ? $list['action']['sections'] : [],
                    ],
                ];
            }
        }

        if (empty($components)) {
            $components = null;
        } else {
            $components = json_decode(json_encode($components), true);
        }

        $messageData = [
            $type => $components,
        ];
        return json_encode($messageData);
    }
}
