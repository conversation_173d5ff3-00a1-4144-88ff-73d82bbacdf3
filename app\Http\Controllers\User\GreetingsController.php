<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Jobs\GreetingSendJob;
use App\Models\Contact;
use App\Models\Device;
use App\Models\Greetings;
use App\Models\Template;
use App\Models\TutorialVideo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class GreetingsController extends Controller
{
    public function index()
    {
        $devices = Device::where('user_id', Auth::id())->where('status', 1)->get();

        $templates = Template::where('user_id', Auth::id())
            // ->where('device_id', $device->id)
            ->where('status', 1)
            ->get();

        $greetings = Greetings::where('user_id', Auth::id())->first();

        $videoTutorial = TutorialVideo::where('key', 'greetings')->first();
        return view('user.greetings.create', compact('devices', 'templates', 'greetings', 'videoTutorial'));
    }

    public function store(Request $request)
    {
        if (api_plan() && api_plan()->title == 'Api') {
            return response()->json([
                'message' => __('Greeting features is not available with your subscription')
            ], 401);
        }

        $device_id = $request->b_device;
        $birth_day_temp = $request->b_temp;
        $b_media_type = $request->b_task_type;
        $b_lang = $request->b_language;
        $b_has_media = $request->b_has_media;
        $b_media_file = null;

        // Upload b_media_file if b_has_media is set to "1"
        if ($b_has_media == 1 && $request->hasFile('b_media_file')) {
            $b_media_file = $this->uploadMediaFile($request->file('b_media_file'));
            if (!$b_media_file) {
                return response()->json([
                    'message' => __('Failed to upload BirthDay Media File.'),
                ], 401);
            }
        }

        $anni_day_temp = $request->a_temp;
        $a_media_type = $request->a_task_type;
        $a_lang = $request->a_language;
        $a_has_media = $request->a_has_media;
        $a_media_file = null;

        // Upload a_media_file if a_has_media is set to "1"
        if ($a_has_media == 1 && $request->hasFile('a_media_file')) {
            $a_media_file = $this->uploadMediaFile($request->file('a_media_file'));
            if (!$a_media_file) {
                return response()->json([
                    'message' => __('Failed to upload Anniversary Media File.'),
                ], 401);
            }
        }

        $greetingsModel = new Greetings();
        $greetingsModel->updateGreetings(
            $device_id,
            $birth_day_temp,
            $b_media_type,
            $b_lang,
            $b_media_file,
            $anni_day_temp,
            $a_media_type,
            $a_lang,
            $a_media_file
        );

        return response()->json([
            'message' => __('Greeting Added Succesfully'),
        ], 200);
    }

    private function uploadMediaFile($file)
    {
        $destinationPath = public_path('uploads/greetings');

        if (!file_exists($destinationPath)) {
            // Create the directory with appropriate permissions (0755)
            mkdir($destinationPath, 0755, true);
        }

        $fileName = uniqid();
        $allowedTypes = ['jpg', 'png', 'pdf', 'mp4', 'jpeg'];

        // Validate the uploaded file
        $extension = $file->getClientOriginalExtension();
        if (!in_array($extension, $allowedTypes)) {
            return false;
        }

        // Move the uploaded file to the destination folder
        $file->move($destinationPath, $fileName . '.' . $extension);

        return $fileName . '.' . $extension;
    }
}
