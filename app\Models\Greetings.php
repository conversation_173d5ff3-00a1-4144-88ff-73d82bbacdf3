<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class Greetings extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'device_id',
        'birth_day_temp',
        'b_media_type',
        'b_lang',
        'birth_day_media',
        'anni_day_temp',
        'anni_day_media',
        'a_media_type',
        'a_lang'
    ];


    public function updateGreetings($device_id, $birth_day_temp, $b_media_type, $b_lang, $b_media_file, $anni_day_temp, $a_media_type, $a_lang, $a_media_file)
    {
        // Check if the user already exists in the 'greetings' table
        $user = Greetings::where('user_id', Auth::id())->where('device_id', $device_id)->first();

        if (!$user) {
            Greetings::create([
                'user_id' => Auth::id(),
                'device_id' => $device_id,
                'birth_day_temp' => $birth_day_temp,
                'b_media_type' => $b_media_type,
                'b_lang' => $b_lang,
                'birth_day_media' => $b_media_file,
                'anni_day_temp' => $anni_day_temp,
                'a_media_type' => $a_media_type,
                'a_lang' => $a_lang,
                'anni_day_media' => $a_media_file
            ]);
        } else {
            $user->update([
                'device_id' => $device_id,
                'birth_day_temp' => $birth_day_temp,
                'b_media_type' => $b_media_type,
                'b_lang' => $b_lang,
                'birth_day_media' => $b_media_file,
                'anni_day_temp' => $anni_day_temp,
                'a_media_type' => $a_media_type,
                'a_lang' => $a_lang,
                'anni_day_media' => $a_media_file
            ]);
        }
        return true;
    }
}
