
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Device'),
        'buttons' => array_filter([
            isset($videoTutorial->value)
                ? [
                    'name' => '<i class="fi-rs-photo-video"></i>&nbsp&nbsp' . __('Video Tutorial'),
                    'url' => '#',
                    'components' => 'data-toggle="modal" data-target="#videoModal"',
                    'is_button' => true,
                ]
                : null,
        ]),
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="row d-flex justify-content-between flex-wrap">
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 total-transfers" id="total-device">
                                        <img src="<?php echo e(asset('uploads/loader.gif')); ?>">
                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                        <i class="fi fi-rs-devices mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                            </p>
                            <h5 class="card-title  text-muted mb-0"><?php echo e(__('Total Accounts')); ?></h5>
                            <p></p>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 total-transfers" id="total-active">
                                        <img src="<?php echo e(asset('uploads/loader.gif')); ?>">
                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                        <i class="fi fi-rs-badge-check mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                            </p>
                            <h5 class="card-title  text-muted mb-0"><?php echo e(__('Active Accounts')); ?></h5>
                            <p></p>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 completed-transfers" id="total-inactive">
                                        <img src="<?php echo e(asset('uploads/loader.gif')); ?>">
                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                        <i class="fi  fi-rs-exclamation mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                            </p>
                            <h5 class="card-title  text-muted mb-0"><?php echo e(__('Inactive Accounts')); ?></h5>
                            <p></p>
                        </div>
                    </div>
                </div>
            </div>

            <?php if(count($devices ?? []) > 0): ?>
                <div class="row">
                    <?php $__currentLoopData = $devices ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-xl-4 col-md-6">
                            <div class="card  border-0">
                                <!-- Card body -->
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col">
                                            <h5 class="card-title text-uppercase text-muted mb-0 text-dark">
                                                <?php echo e($device->name); ?>

                                            </h5>
                                            <div class="mt-3 mb-0">
                                                <span class="pt-2 text-dark"><?php echo e(__('Phone :')); ?>

                                                    <?php if(!empty($device->phone)): ?>
                                                        <?php if($device->status == 1): ?>
                                                            <a href="<?php echo e(url('/user/account/chats/' . $device->uuid)); ?>"
                                                                style="color: #657bf8; font-weight: bold;"><?php echo e($device->phone); ?></a>
                                                        <?php else: ?>
                                                            <span
                                                                style="color: #657bf8; font-weight: bold;"><?php echo e($device->phone); ?></span>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                    <br>
                                                    <br>
                                                    <span class="pt-2 text-dark"><?php echo e(__('Total Messages:')); ?>

                                                        <a
                                                            style="color: #657bf8; font-weight: bold;"><?php echo e(number_format($device->tasks_count)); ?></a>
                                                    </span>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-sm btn-neutral mr-0"
                                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <i class="fas fa-ellipsis-h"></i>
                                            </button>
                                            <div class="dropdown-menu dropdown-menu-right">
                                                <?php if($device->status == 1): ?>
                                                    <a class="dropdown-item has-icon"
                                                        href="<?php echo e(url('/user/account/chats/' . $device->uuid)); ?>"><i
                                                            class="fi fi-rs-comments-question-check"></i><?php echo e(__('Chats')); ?></a>
                                                <?php endif; ?>
                                                <a class="dropdown-item has-icon"
                                                    href="<?php echo e(route('user.account.indiamartIntegration', $device->uuid)); ?>"><i
                                                        class="fi fi-rs-shopping-basket"></i><?php echo e(__('Indiamart Integration')); ?></a>
                                                <a class="dropdown-item has-icon"
                                                    href="https://business.facebook.com/billing_hub/accounts/details/?asset_id=<?php echo e($device->waid); ?>"><i
                                                        class="fi  fi-rs-edit"></i><?php echo e(__('Payment Settings')); ?></a>
                                                <a class="dropdown-item has-icon"
                                                    href="<?php echo e(route('user.account.edit', $device->uuid)); ?>"><i
                                                        class="fi  fi-rs-edit"></i><?php echo e(__('Edit Device')); ?></a>
                                                <form
                                                    action="<?php echo e(route('user.autoSendMktFailMsg', ['uuid' => $device->uuid])); ?>"
                                                    method="POST">
                                                    <?php echo csrf_field(); ?>
                                                    <div class="dropdown-item has-icon">
                                                        <div class="row">
                                                            <div class="col-sm-12 d-flex">
                                                                <label class="mt-1" for="auto_send_mkt_fail_msg">
                                                                    <h5>
                                                                        <?php echo e(__('Autosend Marketing Fail Msg')); ?>&nbsp;&nbsp;
                                                                    </h5>
                                                                </label>
                                                                <label class="custom-toggle custom-toggle-primary">
                                                                    <input type="checkbox" name="auto_send_mkt_fail_msg"
                                                                        id="auto_send_mkt_fail_msg"
                                                                        <?php if($device->auto_send_mkt_fail_msg): ?> checked <?php endif; ?>
                                                                        onchange="this.form.submit()">
                                                                    <span class="custom-toggle-slider rounded-circle"
                                                                        data-label-off="No" data-label-on="Yes"></span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </form>

                                                <a class="dropdown-item has-icon delete-confirm" href="javascript:void(0)"
                                                    data-action="<?php echo e(route('user.account.destroy', $device->uuid)); ?>"><i
                                                        class="fas fa-trash"></i><?php echo e(__('Remove Device')); ?></a>
                                            </div>
                                        </div>
                                    </div>
                                    <p class="mt-3 mb-0 text-sm">
                                        <a class="text-nowrap  font-weight-600"
                                            href="<?php echo e(route('user.statusUpdate', ['uuid' => $device->uuid])); ?>">
                                            <span class="text-dark"><?php echo e(__('Status :')); ?></span>
                                            <span class="badge badge-sm <?php echo e(badge($device->status)['class']); ?>">
                                                <?php echo e($device->status == 1 ? __('Active') : __('Inactive')); ?>

                                            </span>
                                        </a>
                                        
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="alert  bg-gradient-primary text-white"><span
                        class="text-left"><?php echo e(__('Opps There Is No Device Found....')); ?></span></div>
            <?php endif; ?>
        </div>
    </div>

    <input type="hidden" id="base_url" value="<?php echo e(url('/')); ?>">
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/js/pages/user/device.js')); ?>"></script>
    <script>
        const tutorial = <?php echo json_encode($videoTutorial, 15, 512) ?>;

        // Function to extract video ID from YouTube URL
        function getVideoId(url) {
            const regex =
                /(?:https?:\/\/)?(?:www\.)?(?:youtu\.be\/|(?:www\.)?youtube\.com\/(?:watch\?v=|embed\/|v\/|.+\?v=))([^&]+)/;
            const matches = url.match(regex);
            return matches ? matches[1] : null;
        }

        // Event listener for the modal opening
        $('#videoModal').on('show.bs.modal', function() {
            const videoId = getVideoId(tutorial.value); // Extract video ID from the tutorial URL
            if (videoId) {
                const videoUrl = `https://www.youtube.com/embed/${videoId}`;

                // Set the video URL to the iframe
                document.getElementById('youtube-video').src = videoUrl;
            } else {
                console.error('Invalid YouTube URL');
            }
        });

        // Clear the video source when the modal is hidden
        $('#videoModal').on('hidden.bs.modal', function() {
            document.getElementById('youtube-video').src = '';
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/device/index.blade.php ENDPATH**/ ?>