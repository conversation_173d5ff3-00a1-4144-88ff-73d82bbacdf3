@extends('layouts.main.app')
@section('head')
    @include('layouts.main.headersection', ['title' => __('Create Drip Campaign')])
@endsection
@push('topcss')
    {{-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/css/uikit.min.css"> --}}
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/vendor/select2/dist/css/select2.min.css') }}">
@endpush
@section('content')
    <div class="row">
        <div class="col-12 col-sm-12 col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h4>{{ __('Create Drip Campaign') }}</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 col-sm-12 col-md-12">
                            <div class="tab-content no-padding" id="myTab2Content">
                                <div class="tab-pane fade show active" id="home4" role="tabpanel"
                                    aria-labelledby="home-tab4">
                                    <form method="POST" action="{{ route('user.drip_campaign.store') }}" class="ajaxform">
                                        @csrf
                                        <div class="row">
                                            <div class="form-group col-md-6">
                                                <!-- Drip Name field -->
                                                <label for="drip_name">{{ __('Drip Name') }}</label>
                                                <input type="text" class="form-control" name="drip_name" id="drip_name"
                                                    placeholder="Drip Name">
                                            </div>
                                            <div class="form-group col-md-6">
                                                <!-- Select Device Dropdown field -->
                                                <label for="device_name">{{ __('Select Device') }}</label>
                                                <select class="form-control" id="device_id" name="device_id" required
                                                    data-toggle="select">
                                                    <option value="">{{ __('-- Select Device --') }}</option>
                                                    @foreach ($devices as $device)
                                                        <option value="{{ $device['id'] }}">{{ $device['name'] }}
                                                            (+{{ $device['phone'] }})
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <hr>

                                        <div id="dripFieldsContainer">
                                            <div class="row drip-row"
                                                style="border:1px solid #ccc; padding: 5px; margin: 5px;border-radius: 5px;">
                                                <div class="form-group col-md-2">
                                                    <label for="drip_count">{{ __('Drip') }}</label>
                                                    <input type="text" class="form-control drip-count-input"
                                                        name="drip_count[]" id="drip_count" value="Drip 1" readonly>
                                                </div>
                                                <div class="form-group col-md-1">
                                                    <label for="days">{{ __('Days') }}</label>
                                                    <input type="text" class="form-control" name="days[]" id="days"
                                                        placeholder="Days" min="1" max="60" required>
                                                </div>
                                                <div class="form-group col-md-4">
                                                    <label for="drip_template">{{ __('Template') }}</label>
                                                    <select class="form-control" id="drip_template" name="drip_template[]"
                                                        required="" data-toggle="select">
                                                    </select>
                                                </div>
                                                <div class="form-group col-md-1">
                                                    {{-- plus icon --}}
                                                    <label>&nbsp;</label>
                                                    <div>
                                                        <button type="button" class="btn btn-neutral btn-md add_button"
                                                            href="javascript:void(0);">
                                                            <i class="fa fa-plus"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <button
                                                class="btn btn-neutral submit-button float-right">{{ __('Create') }}</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/vendor/select2/dist/js/select2.min.js') }}"></script>
@endpush
@push('js')
    <script>
        $(document).ready(function() {
            // document.getElementById('drip_template').innerText = null;

            $(document).ready(function() {
                function selectSingleDevice() {
                    var deviceDropdown = $("#device_id");
                    if (deviceDropdown.children("option").length == 2) { // One option is the placeholder
                        deviceDropdown.children("option").eq(1).prop('selected', true);
                        deviceDropdown.change();
                    }
                }
                selectSingleDevice();
            });

            $("#device_id").change(function() {
                var selectedDevice = $(this).val();
                $('#drip_template').empty().append(
                    '<option value="" >-- Select Template --</option>');

                if (!selectedDevice) return;

                $.ajax({
                    url: "{{ route('user.get_templates', ':device_id') }}".replace(':device_id',
                        selectedDevice),
                    type: 'GET',
                    success: function(templates) {
                        $.each(templates, function(index, value) {
                            if (value.status == "1") {
                                $('#drip_template').append("<option value='" + value
                                    .id +
                                    "'>" + value.title + " -- " + value.type +
                                    "</option>");
                            }
                        });
                    },
                    error: function(xhr) {
                        console.error('AJAX error:', xhr.responseText);
                    }
                });

            });

            const MAX_DRIPS = 5;
            updateDripCounts();
            updateAddButtonVisibility();

            // Add new row
            $('.add_button').on('click', function() {

                if ($('.drip-row').length >= MAX_DRIPS) {
                    // Optional: Show message to user
                    alert('Maximum ' + MAX_DRIPS + ' drips allowed');
                    return;
                }
                var nextDripNumber = $('.drip-row').length + 1;

                // Get the current device ID
                var deviceId = $("#device_id").val();
                var deviceData = @json($devices);

                // Create new row
                var newRow = `
                            <div class="row drip-row" style="border:1px solid #ccc; padding: 5px; margin: 5px;border-radius: 5px;">
                                <div class="form-group col-md-2">
                                    <label>{{ __('Drip') }}</label>
                                    <input type="text" class="form-control drip-count-input" name="drip_count[]" value="Drip ${nextDripNumber}" readonly>
                                </div>
                                <div class="form-group col-md-1">
                                    <label>{{ __('Days') }}</label>
                                    <input type="text" class="form-control" name="days[]" placeholder="Days" min="1" max="60" required>
                                </div>
                                <div class="form-group col-md-4">
                                    <label>{{ __('Template') }}</label>
                                    <select class="form-control template-select" name="drip_template[]" required>
                                        <option value="">{{ __('-- Select Template --') }}</option>
                                    </select>
                                </div>
                                <div class="form-group col-md-1">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn btn-danger btn-md remove-row">
                                            <i class="fa fa-minus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;

                // Append new row
                $('#dripFieldsContainer').append(newRow);

                updateDripCounts();
                updateAddButtonVisibility();

                // Populate template dropdown for new row
                var $lastRow = $('.drip-row:last');
                populateTemplateDropdown($lastRow, deviceId, deviceData);

                // Initialize template change handler for new row
                // initializeTemplateHandler($lastRow);
            });

            // Remove row
            $(document).on('click', '.remove-row', function() {
                $(this).closest('.drip-row').remove();
                updateDripCounts();
                updateAddButtonVisibility();
            });

            // Function to update all drip counts
            function updateDripCounts() {
                $('.drip-row').each(function(index) {
                    $(this).find('.drip-count-input').val('Drip ' + (index + 1));
                });
            }

            function updateAddButtonVisibility() {
                if ($('.drip-row').length >= MAX_DRIPS) {
                    $('.add_button').hide();
                    // Optional: Add a message or disabled state
                    // $('.add_button').after('<small class="text-danger">Maximum limit reached</small>');
                } else {
                    $('.add_button').show();
                    // Optional: Remove the message if it exists
                    // $('.add_button').next('small').remove();
                }
            }

            function populateTemplateDropdown($row, deviceId, deviceData) {
                console.log('device data is ', deviceData);
                console.log('device id is ', deviceId);
                var $templateSelect = $row.find('.template-select');
                $templateSelect.empty().append('<option value="-1">-- Select Template --</option>');

                $.ajax({
                    url: "{{ route('user.get_templates', ':device_id') }}".replace(':device_id',
                        deviceId),
                    type: 'GET',
                    success: function(templates) {
                        $.each(templates, function(index, value) {
                            if (value.status == "1") {
                                $templateSelect.append("<option value='" + value
                                    .id +
                                    "'>" + value.title + " -- " + value.type +
                                    "</option>");
                            }
                        });
                    },
                    error: function(xhr) {
                        console.error('AJAX error:', xhr.responseText);
                    }
                });

            }


            $('.drip-row').each(function() {
                // initializeTemplateHandler($(this));
            })

            function initializeTemplateHandler($row) {
                $row.find('.template-select').on('change', function() {
                    var deviceId = $("#device_id").val();
                    var templateName = $(this).val();
                    var deviceData = @json($devices);

                    if (deviceId === '-1' || templateName === '-1') return;

                    var template = deviceData[deviceId].templates.find(t => t.name === templateName);
                    if (!template) return;

                    var $mediaSection = $row.find('.media-section');
                    var $mediaFile = $row.find('.media-file');
                    var $hasMediaInput = $row.find('.has-media-input');
                    var $taskTypeInput = $row.find('.task-type-input');
                    var $languageInput = $row.find('.language-input');

                    // Set language
                    $languageInput.val(template.language);

                    // Handle different template types
                    var format = template.components[0].format;
                    switch (format) {
                        case 'IMAGE':
                            setupMediaField($mediaSection, $mediaFile, $hasMediaInput, $taskTypeInput, true,
                                '1', '2');
                            break;
                        case 'DOCUMENT':
                            setupMediaField($mediaSection, $mediaFile, $hasMediaInput, $taskTypeInput, true,
                                '1', '3');
                            break;
                        case 'VIDEO':
                            setupMediaField($mediaSection, $mediaFile, $hasMediaInput, $taskTypeInput, true,
                                '1', '4');
                            break;
                        case 'TEXT':
                            setupMediaField($mediaSection, $mediaFile, $hasMediaInput, $taskTypeInput,
                                false, '0', '1');
                            break;
                        default:
                            setupMediaField($mediaSection, $mediaFile, $hasMediaInput, $taskTypeInput,
                                false, '0', '1');
                    }
                });
            }

            function setupMediaField($mediaSection, $mediaFile, $hasMediaInput, $taskTypeInput, showMedia, hasMedia,
                taskType) {
                if (showMedia) {
                    $mediaSection.show();
                    $mediaFile.prop('disabled', false);
                } else {
                    $mediaSection.hide();
                    $mediaFile.prop('disabled', true);
                }

                $hasMediaInput.val(hasMedia);
                $taskTypeInput.val(taskType);
            }
        });
    </script>
@endpush
