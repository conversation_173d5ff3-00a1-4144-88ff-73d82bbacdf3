
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Facebook Leads'),
        'buttons' => [
            [
                'name' => 'Back',
                'url' => route('user.fbLeadIntList'),
            ],
        ],
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/select2/dist/css/select2.min.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 id="connectedBtn" style="display: none;"><?php echo e(__('Facebook Leads')); ?> <button disabled
                            onclick="launchFacebookLeadSignup()"
                            class="btn btn-outline-primary btn-sm ml-5"><?php echo e(__('Connected to Facebook')); ?> <i
                                class="fi-rs-check"></i></button></h4>
                    <h4 id="notConnectedBtn" style="display: none;"><?php echo e(__('Facebook Leads')); ?> <button
                            onclick="launchFacebookLeadSignup()"
                            class="btn btn-outline-primary btn-sm ml-5"><?php echo e(__('Connect Facebook')); ?> </button></h4>
                </div>
                <div class="card-body" style="margin-top:-25px;">
                    <div class="row">
                        <div class="col-12 col-sm-12 col-md-12">
                            <div class="tab-content no-padding" id="myTab2Content">
                                <div class="tab-pane fade show active" id="mode_1" role="tabpanel"
                                    aria-labelledby="home-tab4">
                                    <div class="row">
                                        <div class="col-sm-12 mb-4" style="display: none;">
                                            <div class="form-group">
                                                <input type="text" id="fb_access_token" name="fb_access_token"
                                                    value="<?php echo e($token); ?>" class="form-control"
                                                    placeholder="Facebook Access Token" readonly>
                                            </div>
                                        </div>
                                        <div class="col-sm-12 mb-4">
                                            <label><?php echo e(__('Page')); ?></label>
                                            <select class="form-control" name="selectPageId" id="selectPageId"
                                                data-toggle="select">
                                                <option value="-1"><?php echo e(__('-- Select Page --')); ?></option>
                                                
                                            </select>
                                        </div>
                                        <div class="col-sm-12 mb-4">
                                            <label><?php echo e(__('Form')); ?></label>
                                            <select class="form-control" name="selectFormId" id="selectFormId"
                                                data-toggle="select">
                                                <option value="-1"><?php echo e(__('-- Select Form --')); ?></option>
                                                
                                            </select>
                                        </div>
                                        <div class="col-sm-12 mb-4">
                                            <button onclick="subscribeBtnClick()"
                                                class="btn btn-outline-primary btn-sm"><?php echo e(__('Connect')); ?></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body" style="display: none;border: 1px solid black;border-radius:5px;"
                         id="templateSection">
                        <h4 style="margin-top:-8px;margin-bottom:13px;"><?php echo e(__('Device & Templates')); ?></h4>
                        <div class="row">
                            <div class="col-12 col-sm-12 col-md-12">
                                <div class="tab-content no-padding" id="myTab2Content">
                                    <div class="tab-pane fade show active" id="mode_1" role="tabpanel"
                                        aria-labelledby="home-tab4">
                                        <form method="POST" action="<?php echo e(route('user.facebook_leads.store')); ?>"
                                            class="ajaxform_reset_form" enctype="multipart/form-data">
                                            <?php echo csrf_field(); ?>
                                            <div class="row">
                                                <div class="col-sm-12  mb-4" style="display: none;">
                                                    page token
                                                    <input type="text" name="page_token" id="page_token"
                                                        class="form-control">
                                                </div>
                                                <div class="col-sm-12  mb-4" style="display: none;">
                                                    page id
                                                    <input type="text" name="page_id" id="page_id"
                                                        class="form-control">
                                                </div>
                                                <div class="col-sm-12  mb-4" style="display: none;">
                                                    page name
                                                    <input type="text" name="page_name" id="page_name"
                                                        class="form-control">
                                                </div>
                                                <div class="col-sm-12  mb-4" style="display: none;">
                                                    form id
                                                    <input type="text" name="form_id" id="form_id"
                                                        class="form-control">
                                                </div>
                                                <div class="col-sm-12  mb-4" style="display: none;">
                                                    form name
                                                    <input type="text" name="form_name" id="form_name"
                                                        class="form-control">
                                                </div>
                                                <div class="col-sm-12  mb-4">
                                                    <label><?php echo e(__('Select Device')); ?></label>
                                                    <select class="form-control" name="device_id" id="device_id"
                                                        data-toggle="select">
                                                        <option value="-1"><?php echo e(__('-- Select Device --')); ?></option>
                                                        <?php $__currentLoopData = $device_data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($device['id']); ?>"><?php echo e($device['name']); ?>

                                                                (+<?php echo e($device['phone']); ?>)
                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                </div>

                                                <div class="col-sm-12  mb-4">
                                                    <label><?php echo e(__('Select Message Template')); ?></label>
                                                    <select class="form-control" name="msg_template" id="msg_template"
                                                        data-toggle="select">
                                                    </select>
                                                </div>
                                                <div class="col-sm-12 mb-4" style="display: none;">
                                                    <div class="form-group">
                                                        <input type="text" id="tmp_lang" name="tmp_lang"
                                                            class="form-control" placeholder="language">
                                                    </div>
                                                </div>
                                                <div class="col-sm-12 mb-4" style="display: none;">
                                                    <div class="form-group">
                                                        <input type="text" id="tmp_type" name="tmp_type"
                                                            class="form-control" placeholder="task_type">
                                                    </div>
                                                </div>
                                                <div class="col-sm-12 mb-4" id="temp_head_image" style="display: none;">
                                                    <div class="form-group">
                                                    </div>
                                                </div>
                                                <div class="col-sm-12 mb-2" id="fb_msg_media" style="display: none;">
                                                    <div class="form-group">
                                                        <label><?php echo e(__('Message Media')); ?></label>
                                                        <input id="tmp_media" type="file" class="form-control"
                                                            name="tmp_media" />
                                                        <small><?php echo e(__(' Supported file type:')); ?></small> <small
                                                            class="text-danger"><?php echo e(__('( jpg, png, pdf, mp4, jpeg )')); ?></small>
                                                    </div>
                                                </div>
                                                <div class="col-sm-12 mb-4" id="check_media" style="display: none;">
                                                    <div class="form-group">
                                                        <input id="has_media" type="textarea" class="form-control"
                                                            name="has_media" value="1" />
                                                    </div>
                                                </div>
                                                <div class="col-sm-12 mb-4" style="display: none;">
                                                    <label><?php echo e(__('Variable')); ?></label>
                                                    <select class="form-control" name="variable" id="variable"
                                                        data-toggle="select">
                                                        
                                                        <option value=""><?php echo e(__('-- Select Yes/No --')); ?></option>
                                                        <option value="true"><?php echo e(__('Yes')); ?></option>
                                                        <option value="false"><?php echo e(__('No')); ?></option>
                                                    </select>
                                                </div>
                                                <div class="col-sm-12  mb-4">
                                                    <label><?php echo e(__('Select Tag')); ?> <small>(optional)</small></label>
                                                    <select class="form-control" name="tag_id" id="tag_id"
                                                        data-toggle="select">
                                                        <option value="-1"><?php echo e(__('-- Select Tag --')); ?></option>
                                                        <?php $__currentLoopData = $tagData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($tag['id']); ?>"><?php echo e($tag['tag_name']); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                </div>
                                                <div class="col-sm-12">
                                                    
                                                    <button type="submit" id="submit-btn"
                                                        class="btn btn-outline-primary submit-button float-left"><?php echo e(__('Save')); ?></button>
                                                    
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>


            </div>
        </div>

        
    <?php $__env->stopSection(); ?>
    <?php $__env->startPush('js'); ?>
        <script src="<?php echo e(asset('assets/vendor/select2/dist/js/select2.min.js')); ?>"></script>
        <script>
            $(document).ready(function() {
                document.getElementById('msg_template').innerText = null;


                $(document).ready(function() {
                    function selectSingleDevice() {
                        var deviceDropdown = $("#device_id");
                        if (deviceDropdown.children("option").length == 2) { // One option is the placeholder
                            deviceDropdown.children("option").eq(1).prop('selected', true);
                            deviceDropdown.change();
                        }
                    }
                    selectSingleDevice();
                });

                $("#device_id").change(function() {
                    var id = $("#device_id").val();
                    let device_data = [];
                    let fb_lead_temp_data = [];

                    device_data = <?php echo json_encode($device_data, 15, 512) ?>;
                    fb_lead_temp_data = <?php echo json_encode($fb_lead_temp_data, 15, 512) ?>;

                    let templates = device_data[id]['templates'];


                    $('#temp_head_image').hide();


                    $('#fb_msg_media').hide();

                    document.getElementById('msg_template').innerText = null;
                    var optionData = '<option value="-1">-- Select Template --</option>';
                    $.each(templates, function(index, value) {
                        if (value.status == "APPROVED") {
                            optionData += "<option value=" + value.name + ">" + value.name + " -- " +
                                value.language + "</option>";
                        }
                    });
                    $('#msg_template').append(optionData);



                    // var media_ext_type = [
                    //     'jpg',
                    //     'jpeg',
                    //     'png',
                    //     'gif',
                    //     'svg', // Images
                    //     'mp4',
                    //     'mov',
                    //     'webm',
                    //     'avi', // Videos
                    //     'pdf',
                    //     'doc',
                    //     'docx',
                    //     'ppt',
                    //     'pptx',
                    //     'xls',
                    //     'xlsx',
                    //     'csv',
                    //     'txt', // Documents
                    //     'zip',
                    //     'rar',
                    //     '7z', // Archive Files
                    //     'mp3',
                    //     'wav',
                    //     'flac',
                    //     'ogg', // Audio Files
                    // ]
                    // if (fb_lead_temp_data[id]) {
                    //     let msg_template = fb_lead_temp_data[id]['msg_template'];
                    //     let tmp_lang = fb_lead_temp_data[id]['tmp_lang'];
                    //     let b_media = fb_lead_temp_data[id]['tmp_media'];
                    //     // let im_key = fb_lead_temp_data[id]['im_key'];
                    //     if (msg_template) {
                    //         $('#msg_template').val(msg_template);
                    //         // $('#im_key').val(im_key);
                    //         $("#tmp_lang").val(tmp_lang);
                    //         if (b_media) {
                    //             var b_media_url = '<?php echo e(asset('uploads/indiamart_integration_media')); ?>/' +
                    //                 b_media;

                    //             let b_ext = b_media.split('.').pop();
                    //             var b_html = '';
                    //             if (media_ext_type.includes(b_ext)) {
                    //                 if (['jpg', 'jpeg', 'png', 'gif', 'svg'].includes(b_ext)) {
                    //                     b_html +=
                    //                         `<img src="${b_media_url}" class="img-fluid" style="width:180px;"></img><br>`
                    //                 } else if (['mp4', 'mov', 'webm', 'avi'].includes(b_ext)) {
                    //                     b_html +=
                    //                         `<video src="${b_media_url}" controls style="width:180px;"></video><br>`
                    //                 } else if (
                    //                     ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'csv'].includes(
                    //                         b_ext)
                    //                 ) {
                    //                     if (b_ext === 'pdf') {
                    //                         b_html +=
                    //                             `<a href="${b_media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/9496/9496432.png" style="width:180px;"></a><br>`
                    //                     } else if (b_ext === 'doc' || b_ext === 'docx') {
                    //                         b_html +=
                    //                             `<a href="${b_media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/5968/5968517.png" style="width:180px;"></a><br>`
                    //                     } else if (b_ext === 'pptx') {
                    //                         b_html +=
                    //                             `<a href="${b_media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/732/732224.png" style="width:180px;"></a><br>`
                    //                     } else if (b_ext === 'xls' || b_ext === 'xlsx') {
                    //                         b_html +=
                    //                             `<a href="${b_media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/4726/4726040.png" style="width:180px;"></a><br>`
                    //                     } else if (b_ext === 'csv') {
                    //                         b_html +=
                    //                             `<a href="${b_media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/28/28842.png" style="width:180px;"></a><br>`
                    //                     } else if (b_ext === 'txt') {
                    //                         b_html +=
                    //                             `<a href="${b_media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/337/337956.png" style="width:180px;"></a><br>`
                    //                     }
                    //                 } else if (['zip', 'rar', '7z'].includes(b_ext)) {
                    //                     if (b_ext === 'zip') {
                    //                         b_html +=
                    //                             `<a href="${b_media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/28/28814.png" style="width:180px;"></a><br>`
                    //                     } else if (b_ext === 'rar') {
                    //                         b_html +=
                    //                             `<a href="${b_media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/28/28792.png" style="width:180px;"></a><br>`
                    //                     } else if (b_ext === '7z') {
                    //                         b_html +=
                    //                             `<a href="${b_media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/29/29142.png" style="width:180px;"></a><br>`
                    //                     }
                    //                 } else if (['mp3', 'wav', 'flac', 'ogg'].includes(b_ext)) {
                    //                     if (b_ext === 'mp3') {
                    //                         b_html +=
                    //                             `<a href="${b_media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/81/81281.png" style="width:180px;"></a><br>`
                    //                     } else if (b_ext === 'wav') {
                    //                         b_html +=
                    //                             `<a href="${b_media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/256/29/29101.png" style="width:180px;"></a><br>`
                    //                     } else if (b_ext === 'flac') {
                    //                         b_html +=
                    //                             `<a href="${b_media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/8300/8300654.png" style="width:180px;"></a><br>`
                    //                     } else if (b_ext === 'ogg') {
                    //                         b_html +=
                    //                             `<a href="${b_media_url}" target="_blank"><img src="https://cdn-icons-png.flaticon.com/512/9224/9224315.png" style="width:180px;"></a><br>`
                    //                     }
                    //                 }
                    //             }
                    //             $('#temp_head_image').show();
                    //             $('#temp_head_image').html(b_html);
                    //             $('#fb_msg_media').show();
                    //         } else {
                    //             $('#temp_head_image').hide();
                    //         }
                    //     }
                    // }
                });
                $("#msg_template").change(function() {
                    let device_data = [];
                    var id = $("#msg_template").val();
                    device_data = <?php echo json_encode($device_data, 15, 512) ?>;
                    var uuid = $("#device_id").val();
                    let templates = [];
                    templates = device_data[uuid]['templates'];
                    //console.log('template is ', templates);

                    $.each(templates, function(index, value) {
                        if (value['name'] == id) {
                            var head_type = value['components'][0]['format'];

                            var varCheck = value['components'][1]['text'];
                            let matches = varCheck.match(/\{\{\d+\}\}/g);
                            //console.log('match is ', matches);

                            let selectElement = document.getElementById("variable");
                            //console.log('element is ', selectElement);


                            if (matches && matches.length > 0) {
                                if (matches.length > 1) {
                                    $('#variable').val('false').change();
                                    $('#submit-btn').prop('disabled', true);
                                    ToastAlert('error', 'only template with one variable is supported');
                                } else {
                                    $('#variable').val('true').change();
                                    $('#submit-btn').prop('disabled', false);
                                }
                                //selectElement.value = "true"; // Select "Yes" if variables exist
                            } else {
                                $('#variable').val('false').change();
                                $('#submit-btn').prop('disabled', false);
                                //selectElement.value = "false"; // Select "No" if no variables are found
                            }

                            var language = value['language'];
                            $("#tmp_lang").val(language);

                            if (head_type == "IMAGE") {
                                //set the image src
                                $("#tmp_media").removeAttr("disabled");
                                $('#fb_msg_media').show();
                                $("#has_media").val("1");
                                $("#tmp_type").val("2");

                            } else if (head_type == "DOCUMENT") {
                                $('#fb_msg_media').show();
                                $("#tmp_media").removeAttr("disabled");
                                $("#has_media").val("1");
                                $("#tmp_type").val("3");

                            } else if (head_type == "VIDEO") {
                                $("#has_media").val("1");
                                $("#tmp_type").val("4");
                                $('#fb_msg_media').show();
                                $("#tmp_media").removeAttr("disabled");

                            } else if (head_type == "TEXT") {
                                $("#has_media").val("0");
                                $("#tmp_type").val("1");
                                $('#fb_msg_media').show();
                                //disable upload media
                                $("#tmp_media").prop('disabled', true);
                                $('#msg_media').val(null);

                            } else {
                                $("#has_media").val("0");
                                $("#tmp_type").val("1");
                                // $("#tmp_media").removeAttr("disabled");
                                var text = value['components'][0]['text'];
                            }

                            var tmpValue = $('#tmp_type').val();
                            // $("#tmp_type").val("2");
                            if (tmpValue == '1') {
                                $("#msg_media").prop('disabled', true);
                                $('#msg_media').val(null);
                            } else {
                                $('#msg_media').prop('disabled', false);
                            }
                        }
                    });
                });
            });
        </script>

        <script>
            window.fbAsyncInit = function() {
                FB.init({
                    appId: "<?php echo e(config('FB_APP_ID', env('FB_APP_ID'))); ?>",
                    autoLogAppEvents: true,
                    xfbml: true,
                    version: 'v18.0'
                });
            };
        </script>
        <script async defer crossorigin="anonymous" src="https://connect.facebook.net/en_US/sdk.js"></script>
        
        <script>
            // Facebook Login with JavaScript SDK
            function launchFacebookLeadSignup() {
                // Launch Facebook login
                $("#loader").show();
                FB.login(function(response) {
                    if (response.authResponse) {
                        var code = response.authResponse.code;
                        $("#loader").hide();
                        // console.log(response);
                        // post code to route device.embeded-signup
                        $.ajaxSetup({
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            }
                        });
                        $("#loader").show();
                        $.ajax({
                            url: '/user/fb_lead_ads_signup',
                            type: 'POST',
                            dataType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                code: code
                            }),
                            success: function(data) {
                                // console.log('fb_lead_ads_signup data: ', data);

                                $("#loader").hide();
                                //json decode get message and redirect to device.index
                                //var message = JSON.parse(data.message);
                                ToastAlert('success', data.message);
                                // window.location.href = "<?php echo e(route('user.device.index')); ?>";
                                setInterval(() => {
                                    window.location.reload();
                                }, 2000);
                            },
                            error: function(error) {
                                $("#loader").hide();
                                ToastAlert('error', error.message);

                            }
                        });


                    } else {
                        $("#loader").hide();
                        // console.log('User cancelled login or did not fully authorize.');
                    }
                }, {
                    config_id: "<?php echo e(config('FB_LEAD_CONF_ID', env('FB_LEAD_CONF_ID'))); ?>", // configuration ID obtained in the previous step goes here
                    response_type: 'code', // must be set to 'code' for System User access token
                    override_default_response_type: true,
                    extras: {
                        setup: {

                        }
                    }
                });
            }
        </script>

        <script>
            function getPageList(accessToken) {
                $.ajax({
                    // url: 'user.leadAccessToken/'+'',
                    url: "<?php echo e(url('user/leadAccessToken')); ?>/" + accessToken,
                    type: 'GET',
                    dataType: 'json',
                    contentType: 'application/json',
                    success: function(data) {

                        if (data.data) {
                            var $select = $("#selectPageId");
                            $select.empty();
                            $select.append('<option value="-1">-- Select Page --</option>');

                            // Loop through each item in the data array
                            $.each(data.data, function(index, page) {
                                // console.log('page id is', page.id);
                                // console.log('page name is', page.name);

                                // Append a new option with the page's id and name
                                $select.append(
                                    $('<option></option>').val(page.id).text(page.name).data(
                                        'access-token', page.access_token)
                                );
                            });

                            $select.trigger('change');
                            // getFormList()
                            $("#selectPageId").change(function() {
                                getFormList(); // Call getFormList when the page selection changes
                            });
                        }
                    },
                    error: function(error) {
                        console.log('Error:', error);
                        ToastAlert('error', 'Failed to load pages reload again.');
                    }
                });
            }

            $(document).ready(function() {
                var fbAccToken = $("#fb_access_token").val();
                //console.log('fb token is ', fbAccToken);
                if (fbAccToken == null || fbAccToken == '') {
                    //  ToastAlert('error', 'Connect with facebook to get token.');
                    // 
                    $("#connectedBtn").hide();
                    $("#notConnectedBtn").show();
                } else {
                    getPageList(fbAccToken);
                    $("#connectedBtn").show();
                    $("#notConnectedBtn").hide();
                }
            });

            function getFormList() {
                var pageId = $("#selectPageId").val();
                var accessToken = $("option:selected", "#selectPageId").data("access-token");

                $("#page_id").val(pageId); // add page id to hidden field

                // Check if a valid page is selected
                if (pageId === "-1" || !accessToken) {
                    ToastAlert('error', 'Please select a page.');
                    // alert("Please select a page.");
                    return;
                }
                // Construct the URL to check subscription status
                var url = `https://graph.facebook.com/v21.0/${pageId}/leadgen_forms?access_token=${accessToken}`;
                //console.log('url is ', url);

                // Make the AJAX request to Facebook Graph API
                $.ajax({
                    url: url,
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        //console.log('form data', response.data); // Log the response for debugging

                        if (response.data) {
                            var $select = $("#selectFormId");
                            $select.empty();
                            $select.append('<option value="-1">-- Select Form --</option>');

                            // Loop through each item in the data array
                            $.each(response.data, function(index, form) {
                                // console.log('form id is', form.id);
                                // console.log('form name is', form.name);

                                // Append a new option with the page's id and name
                                $select.append(
                                    $('<option></option>').val(form.id).text(form.name)
                                );
                            });

                            // Optionally, you can trigger a re-initialization if you're using a select plugin like select2
                            $select.trigger('change');
                        }

                    },
                    error: function(error) {
                        console.log('Error:', error);
                        ToastAlert('error', 'Failed to load forms.');
                    }
                });
            }

            // Handle page selection change
            $("#selectPageId").change(function() {
                var pageId = $("#selectPageId").val();
                var pageName = $("option:selected", "#selectPageId").text();
                var accessToken = $("option:selected", "#selectPageId").data("access-token");

                $("#page_id").val(pageId);
                $("#page_name").val(pageName);
                $("#page_token").val(accessToken);
            });

            // Handle form selection change
            $("#selectFormId").change(function() {
                var formId = $("#selectFormId").val();
                var formName = $("option:selected", "#selectFormId").text();

                $("#form_id").val(formId);
                $("#form_name").val(formName);
            });

            function subscribeBtnClick() {

                var pageId = $("#selectPageId").val();
                var pageName = $("option:selected", "#selectPageId").text();
                var formId = $("#selectFormId").val();
                var formName = $("option:selected", "#selectFormId").text();
                var accessToken = $("option:selected", "#selectPageId").data("access-token");

                $("#page_id").val(pageId);
                $("#page_name").val(pageName);
                $("#page_token").val(accessToken);
                $("#form_id").val(formId);
                $("#form_name").val(formName);

                // Check if a valid page is selected
                if (pageId === "-1") {
                    ToastAlert('error', 'Please select a page.');
                    // alert("Please select a page.");
                    return;
                }

                if (formId === "-1") {
                    ToastAlert('error', 'Please select a form.');
                    // alert("Please select a page.");
                    return;
                }
                // Construct the URL to check subscription status
                var url =
                    `https://graph.facebook.com/${pageId}/subscribed_apps?subscribed_fields=leadgen&access_token=${accessToken}`;
                //console.log('Subscription url is ', url);

                // Make the AJAX request to Facebook Graph API
                $.ajax({
                    url: url,
                    type: 'POST',
                    dataType: 'json',
                    success: function(response) {
                        //console.log(response); // Log the response for debugging

                        // Check if the response is successful
                        if (response.success) {
                            // Show the template section if the subscription is successful
                            $("#templateSection").show();
                        } else {
                            // ToastAlert('success', data.message);
                            ToastAlert('error', 'Failed to connect.');
                            // alert("Failed to subscribe.");
                            $("#templateSection").hide();
                        }
                    },
                    error: function(error) {
                        console.error('Error subscribing:', error);
                        ToastAlert('error', 'There was an error processing your request.');
                        // alert("There was an error processing your request.");
                    }
                });
            }
        </script>
    <?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/facebook_leads/create.blade.php ENDPATH**/ ?>