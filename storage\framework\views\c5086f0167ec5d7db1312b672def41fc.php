
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/css/uikit.min.css">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/select2/dist/css/select2.min.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row justify-content-center">
        <div class="col-12">
            <?php if(api_plan() && api_plan()->title == 'Api'): ?>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="alert bg-gradient-primary text-white alert-dismissible fade show" role="alert">
                            <span class="alert-icon"><i class="fi  fi-rs-info text-white"></i></span>
                            <span class="alert-text">
                                <strong><?php echo e(__('!Opps ')); ?></strong>
                                <?php echo e(__('Indiamart integration features is not available in your subscription plan')); ?>

                            </span>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            <div class="card">
                <div class="card-header">
                    <h4><?php echo e(__('Indiamart Integration')); ?></h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 col-sm-12 col-md-12">
                            <div class="tab-content no-padding" id="myTab2Content">
                                <div class="tab-pane fade show active" id="mode_1" role="tabpanel"
                                    aria-labelledby="home-tab4">
                                    <form method="POST" action="<?php echo e(route('user.account.indMartIntStore')); ?>"
                                        class="ajaxform_reset_form" enctype="multipart/form-data">
                                        <?php echo csrf_field(); ?>
                                        <div class="row">
                                            <div class="col-sm-12  mb-4">
                                                <label><?php echo e(__('Device Name')); ?></label>
                                                <input type="text" value="<?php echo e($device->name); ?> (+<?php echo e($device->phone); ?>)"
                                                    class="form-control" readonly>
                                            </div>
                                            <div class="col-sm-12 mb-4" style="display: none;">
                                                <label><?php echo e(__('Device Id')); ?></label>
                                                <input type="text" value="<?php echo e($device->id); ?>" name="device_id"
                                                    id="device_id" class="form-control" readonly>
                                            </div>
                                            
                                            <div class="col-sm-12  mb-4">
                                                <label><?php echo e(__('Select Message Template')); ?></label>
                                                <select class="form-control" name="msg_template" id="msg_template"
                                                    data-toggle="select">
                                                </select>
                                            </div>
                                            <div class="col-sm-12 mb-4">
                                                <label><?php echo e(__('Indiamart Key')); ?></label>
                                                <textarea name="im_key" id="im_key" cols="30" rows="2" class="form-control"></textarea>
                                            </div>
                                            <div class="col-sm-12">
                                                <div class="col-sm-12">
                                                    
                                                    
                                                    <a href="<?php echo e(route('user.deleteImIntegration', $device['uuid'])); ?>"
                                                        id="delete_im_integration" style="display: none;"
                                                        class="btn btn-outline-danger float-left"><?php echo e(__('Delete Integration')); ?>

                                                    </a>
                                                    
                                                    <button type="submit"
                                                        class="btn btn-outline-primary submit-button float-right"><?php echo e(__('Save')); ?></button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/vendor/select2/dist/js/select2.min.js')); ?>"></script>
    <script src="https://woody180.github.io/vanilla-javascript-emoji-picker/vanillaEmojiPicker.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/js/uikit.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/js/uikit-icons.min.js"></script>

    <script type="text/javascript" src="<?php echo e(asset('assets/js/pages/bulk/template.js')); ?>"></script>

    <script>
        $(document).ready(function() {
            document.getElementById('msg_template').innerText = null;

            

            // $("#device_id").change(function() {
            // var selectedDevice = $(this).val();
            var selectedDevice = $('#device_id').val();
            // console.log('selectedDevice is ', selectedDevice);
            $('#msg_template').empty().append(
                '<option value="" >-- Select Template --</option>');

            if (!selectedDevice) return;

            $.ajax({
                url: "<?php echo e(route('user.get_templates', ':device_id')); ?>".replace(':device_id',
                    selectedDevice),
                type: 'GET',
                success: function(templates) {
                    $.each(templates, function(index, value) {
                        if (value.status == "1") {
                            $('#msg_template').append("<option value='" + value
                                .id +
                                "'>" + value.title + " -- " + value.type +
                                "</option>");
                        }
                    });
                },
                error: function(xhr) {
                    console.error('AJAX error:', xhr.responseText);
                }
            });

            // });

            // if already integrated then show delete button and select the template
            var indIntegration = <?php echo json_encode($im_templates, 15, 512) ?>;
            if (indIntegration) {
                $('#delete_im_integration').show();
                var msg_template = $('#msg_template').val();
                $('#msg_template').val(msg_template);
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/device/indiamart_integration.blade.php ENDPATH**/ ?>