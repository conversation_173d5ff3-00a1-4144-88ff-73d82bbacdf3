@extends('layouts.main.app')
@section('head')
    @include('layouts.main.headersection', ['title' => __('Edit Drip Campaign')])
@endsection
@push('topcss')
    {{-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/css/uikit.min.css"> --}}
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/vendor/select2/dist/css/select2.min.css') }}">
@endpush
@section('content')
    <div class="row">
        <div class="col-12 col-sm-12 col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h4>{{ __('Edit Drip Campaign') }}</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 col-sm-12 col-md-12">
                            <div class="tab-content no-padding" id="myTab2Content">
                                <div class="tab-pane fade show active" id="home4" role="tabpanel"
                                    aria-labelledby="home-tab4">
                                    <form method="POST"
                                        action="{{ route('user.drip_campaign.update', $drip_campaign->id) }}"
                                        class="ajaxform">
                                        @csrf
                                        @method('PUT')
                                        <div class="row">
                                            <div class="form-group col-md-6">
                                                <!-- Drip Name field -->
                                                <label for="drip_name">{{ __('Drip Name') }}</label>
                                                <input type="text" class="form-control" name="drip_name" id="drip_name"
                                                    value="{{ $drip_campaign->drip_name }}" required>
                                            </div>
                                            <div class="form-group col-md-6">
                                                <!-- Select Device Dropdown field -->
                                                <label for="device_name">{{ __('Select Device') }}</label>
                                                <select class="form-control" id="device_id" name="device_id" required
                                                    data-toggle="select">
                                                    <option value="">{{ __('-- Select Device --') }}</option>
                                                    @foreach ($devices as $device)
                                                        <option value="{{ $device['id'] }}"
                                                            @if ($drip_campaign->device_id == $device['id']) selected @endif>
                                                            {{ $device['name'] }}(+{{ $device['phone'] }})
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <hr>

                                        <div id="dripFieldsContainer">
                                            @foreach ($drip_campaign->drip_data as $drip)
                                                <div class="row drip-row"
                                                    style="border:1px solid #ccc; padding: 5px; margin: 5px;border-radius: 5px;">
                                                    <div class="form-group col-md-2">
                                                        <label for="drip_count">{{ __('Drip') }}</label>
                                                        <input type="text" class="form-control drip-count-input"
                                                            name="drip_count[]" value="{{ $drip['drip_count'] }}" readonly>
                                                    </div>
                                                    <div class="form-group col-md-1">
                                                        <label for="days">{{ __('Days') }}</label>
                                                        <input type="text" class="form-control" name="days[]"
                                                            value="{{ $drip['days'] }}" min="1" max="60"
                                                            required>
                                                    </div>
                                                    <div class="form-group col-md-4">
                                                        <label for="drip_template">{{ __('Template') }}</label>
                                                        <select class="form-control template-select" name="drip_template[]"
                                                            required data-toggle="select" data-current="{{ $drip['template'] }}">
                                                            <option value="">{{ __('-- Select Template --') }}
                                                            </option>
                                                        </select>
                                                    </div>
                                                    @if ($drip['media_path'])
                                                        <div class="form-group col-md-2">
                                                            <label>&nbsp;</label>
                                                            {{-- <label for="media_file">{{ __('Preview') }}</label> --}}
                                                            <a href="{{ asset($drip['media_path']) }}"
                                                                target="_blank">{{ __('View Media') }}</a>
                                                        </div>
                                                    @endif
                                                    <div class="form-group col-md-1">
                                                        <label>&nbsp;</label>
                                                        <div>
                                                            <!-- Button will be dynamically updated by JavaScript -->
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                        <div class="form-group">
                                            <button
                                                class="btn btn-neutral submit-button float-right">{{ __('Update') }}</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/vendor/select2/dist/js/select2.min.js') }}"></script>
@endpush
@push('js')
    <script>
        $(document).ready(function() {
            const MAX_DRIPS = 5;

            // Initial setup for existing rows
            initializeExistingRows();
            updateDripCounts();
            updateRowButtons();

            function initializeExistingRows() {
                // Load templates for each existing row
                $('.drip-row').each(function(index) {
                    const $row = $(this);
                    const deviceId = $('#device_id').val();
                    const currentTemplate = $row.find('select[name="drip_template[]"]').data('current');

                    // Replace existing buttons with dynamic buttons
                    updateRowActionButtons($row, index);

                    // Populate template dropdown and select current value
                    populateTemplateDropdown($row, deviceId, currentTemplate);
                    // initializeTemplateHandler($row);
                });
            }

            function updateRowActionButtons($row, index) {
                const $buttonContainer = $row.find('.form-group:last-child div');
                $buttonContainer.empty();

                if (index === 0) {
                    // First row: Show only add button if under max limit
                    if ($('.drip-row').length < MAX_DRIPS) {
                        $buttonContainer.append(`
                            <button type="button" class="btn btn-neutral btn-md add_button">
                                <i class="fa fa-plus"></i>
                            </button>
                        `);
                    }
                } else {
                    // Other rows: Show remove button
                    $buttonContainer.append(`
                        <button type="button" class="btn btn-danger btn-md remove-row">
                            <i class="fa fa-minus"></i>
                        </button>
                    `);
                }
            }

            function updateRowButtons() {
                $('.drip-row').each(function(index) {
                    updateRowActionButtons($(this), index);
                });
            }

            // Handle device change
            $("#device_id").change(function() {
                const deviceId = $(this).val();

                if (!deviceId) {
                    // Clear all template dropdowns if no device selected
                    $('.template-select').empty().append('<option value="">-- Select Template --</option>');
                    return;
                }

                // Update template dropdowns for all rows
                $('.drip-row').each(function() {
                    populateTemplateDropdown($(this), deviceId);
                });
            });

            // Add new row
            $(document).on('click', '.add_button', function() {
                if ($('.drip-row').length >= MAX_DRIPS) {
                    alert('Maximum ' + MAX_DRIPS + ' drips allowed');
                    return;
                }

                const nextDripNumber = $('.drip-row').length + 1;
                const deviceId = $("#device_id").val();

                const newRow = `
                    <div class="row drip-row" style="border:1px solid #ccc; padding: 5px; margin: 5px;border-radius: 5px;">
                        <div class="form-group col-md-2">
                            <label>{{ __('Drip') }}</label>
                            <input type="text" class="form-control drip-count-input" name="drip_count[]" value="Drip ${nextDripNumber}" readonly>
                        </div>
                        <div class="form-group col-md-1">
                            <label>{{ __('Days') }}</label>
                            <input type="text" class="form-control" name="days[]" placeholder="Days" min="1" max="60" required>
                        </div>
                        <div class="form-group col-md-4">
                            <label>{{ __('Template') }}</label>
                            <select class="form-control template-select" name="drip_template[]" data-toggle="select" required>
                                <option value="">{{ __('-- Select Template --') }}</option>
                            </select>
                        </div>
                        <div class="form-group col-md-1">
                            <label>&nbsp;</label>
                            <div></div>
                        </div>
                    </div>
                `;

                $('#dripFieldsContainer').append(newRow);
                const $newRow = $('.drip-row:last');

                populateTemplateDropdown($newRow, deviceId);
                // initializeTemplateHandler($newRow);
                updateRowButtons();
            });

            // Remove row
            $(document).on('click', '.remove-row', function() {
                $(this).closest('.drip-row').remove();
                updateDripCounts();
                updateRowButtons();
            });

            function populateTemplateDropdown($row, deviceId, currentTemplate = null) {
                const $templateSelect = $row.find('.template-select');

                // If currentTemplate is not provided, get it from data attribute
                if (!currentTemplate) {
                    currentTemplate = $templateSelect.data('current');
                }

                $templateSelect.empty().append('<option value="">-- Select Template --</option>');

                if (!deviceId) return;

                $.ajax({
                    url: "{{ route('user.get_templates', ':device_id') }}".replace(':device_id', deviceId),
                    type: 'GET',
                    success: function(templates) {
                        $.each(templates, function(index, value) {
                            if (value.status == "1") {
                                // Compare with template ID (stored value) instead of name
                                const selected = currentTemplate && value.id ==
                                    currentTemplate ? 'selected' : '';
                                $templateSelect.append(`
                                    <option value="${value.id}" ${selected}>
                                        ${value.title} -- ${value.type}
                                    </option>
                                `);
                            }
                        });
                    },
                    error: function(xhr) {
                        console.error('AJAX error:', xhr.responseText);
                    }
                });
            }

            function initializeTemplateHandler($row) {
                $row.find('.template-select').on('change', function() {
                    const deviceId = $("#device_id").val();
                    const templateName = $(this).val();
                    const deviceData = [];

                    if (!deviceId || !templateName) return;

                    const template = deviceData[deviceId].templates.find(t => t.name === templateName);
                    if (!template) return;

                    updateRowSettings($row, template);
                });

                // Trigger change event for existing selections
                const selectedTemplate = $row.find('.template-select').val();
                if (selectedTemplate) {
                    $row.find('.template-select').trigger('change');
                }
            }

            function updateRowSettings($row, template) {
                const $mediaSection = $row.find('.media-section');
                const $mediaFile = $row.find('.media-file');
                const $hasMediaInput = $row.find('.has-media-input');
                const $taskTypeInput = $row.find('.task-type-input');
                const $languageInput = $row.find('.language-input');

                // Set language
                $languageInput.val(template.language);

                // Handle media settings based on template format
                const format = template.components[0].format;
                const mediaSettings = {
                    'IMAGE': {
                        showMedia: true,
                        hasMedia: '1',
                        taskType: '2'
                    },
                    'DOCUMENT': {
                        showMedia: true,
                        hasMedia: '1',
                        taskType: '3'
                    },
                    'VIDEO': {
                        showMedia: true,
                        hasMedia: '1',
                        taskType: '4'
                    },
                    'TEXT': {
                        showMedia: false,
                        hasMedia: '0',
                        taskType: '1'
                    }
                };

                const settings = mediaSettings[format] || mediaSettings['TEXT'];

                $mediaSection.toggle(settings.showMedia);
                $mediaFile.prop('disabled', !settings.showMedia);
                $hasMediaInput.val(settings.hasMedia);
                $taskTypeInput.val(settings.taskType);
            }

            function updateDripCounts() {
                $('.drip-row').each(function(index) {
                    $(this).find('.drip-count-input').val('Drip ' + (index + 1));
                });
            }

            function updateAddButtonVisibility() {
                $('.add_button').toggle($('.drip-row').length < MAX_DRIPS);
            }
        });
    </script>
@endpush
