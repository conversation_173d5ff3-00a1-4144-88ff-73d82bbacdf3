[2025-06-03 11:53:24] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AVNi7dnT6Wpf9Q-z8jYPHgm"}}  
[2025-06-03 11:53:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"Ad6OlykcMO9OEmwGjjhLfrf"}}  
[2025-06-03 11:53:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"A1pB6ji5F9Y_oisXxKi-I_E"}}  
[2025-06-03 11:54:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AdSO6Vd72mpYB0PiVQefgZ-"}}  
[2025-06-03 11:54:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"Amzy5uT5J70OiTqJbknzsHk"}}  
[2025-06-03 11:55:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AGyKHTc3Y9U2n0cxvqHVSLd"}}  
[2025-06-03 11:55:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AfdKOr20w0CB-_u5KDUU017"}}  
[2025-06-03 11:56:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"ALBWhFJkbG8Ho7rSCpU4syz"}}  
[2025-06-03 11:56:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AvqhP0s-U6YxeBEks9lNKQi"}}  
[2025-06-03 11:57:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AZeOu0konYBVtnfCUPE7Su_"}}  
[2025-06-03 11:57:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"A9KwirusGmMhuFRQTvMrxkG"}}  
[2025-06-03 11:58:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"A8x-s7DeYKlpWTrj0Cu0R32"}}  
[2025-06-03 11:58:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"Au37oeadAbX-nP1xGFWh0i6"}}  
[2025-06-03 11:59:25] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_id' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, CASE
            WHEN task.task_type = 1 THEN 'TEXT'
            WHEN task.task_type = 2 THEN 'IMAGE'
            WHEN task.task_type = 3 THEN 'PDF'
            WHEN task.task_type = 4 THEN 'VIDEO'
            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE
            WHEN task.task_status = 0 THEN 'Pending'
            WHEN task.task_status = 1 THEN 'sent'
            WHEN task.task_status = 3 THEN 'Read'
            WHEN task.task_status = 2 THEN 'delivered'
            WHEN task.task_status = 4 THEN 'failed'
            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` inner join `templates` on `task`.`templateId` = `templates`.`template_id` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_id' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, CASE

            WHEN task.task_type = 1 THEN 'TEXT'

            WHEN task.task_type = 2 THEN 'IMAGE'

            WHEN task.task_type = 3 THEN 'PDF'

            WHEN task.task_type = 4 THEN 'VIDEO'

            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE

            WHEN task.task_status = 0 THEN 'Pending'

            WHEN task.task_status = 1 THEN 'sent'

            WHEN task.task_status = 3 THEN 'Read'

            WHEN task.task_status = 2 THEN 'delivered'

            WHEN task.task_status = 4 THEN 'failed'

            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` inner join `templates` on `task`.`templateId` = `templates`.`template_id` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(87): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '1', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_id' in 'on clause' at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select `task`.`...')
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `task`.`...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(87): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '1', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 {main}
"} 
[2025-06-03 11:59:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AqbOOymbvgF0vDWjnNgvqkZ"}}  
[2025-06-03 11:59:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"ANHKPW3wdPWjVWRCdBqec9c"}}  
[2025-06-03 12:00:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AmLihJqSNh7M3ffNBPTng7V"}}  
[2025-06-03 12:00:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"Ax2by0kdk2TO64gmzoN-o1x"}}  
[2025-06-03 12:01:27] local.INFO: data is {"task_id":137,"is_reply":0,"created_by":2,"buttons":null,"text":null,"scheduled_on":"2025-06-03 12:01:25","send_to_number":"9510991141","templateId":"1","language":"en","task_url":null,"campaign_name":null,"parameters":null,"whatsapp_id":"344b601a-2cfa-4f82-ade2-0ec6e5f406e4","task_type":"text","id":1,"phone":"918320677031","status":1,"waid":null,"phoneid":null,"token":"0b6345e5-710a-438f-af94-fd810d025250","is_blacklisted":0}  
[2025-06-03 12:01:27] local.INFO: RCS API key not found in env  
[2025-06-03 12:01:27] local.INFO: Balance added to user  
[2025-06-03 12:01:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"A2FRUa_e4tD3vVoIzoQeTiL"}}  
[2025-06-03 12:01:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AB88Zoa1lZGnqVApb93e27z"}}  
[2025-06-03 12:02:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AD8j3G6Ckf39VI23VFkgnkD"}}  
[2025-06-03 12:02:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AUBj3CrT1Gq6Zan9jHBnysl"}}  
[2025-06-03 12:02:45] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_name' in 'field list' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`template_name`, CASE
            WHEN task.task_type = 1 THEN 'TEXT'
            WHEN task.task_type = 2 THEN 'IMAGE'
            WHEN task.task_type = 3 THEN 'PDF'
            WHEN task.task_type = 4 THEN 'VIDEO'
            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE
            WHEN task.task_status = 0 THEN 'Pending'
            WHEN task.task_status = 1 THEN 'sent'
            WHEN task.task_status = 3 THEN 'Read'
            WHEN task.task_status = 2 THEN 'delivered'
            WHEN task.task_status = 4 THEN 'failed'
            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_name' in 'field list' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`template_name`, CASE

            WHEN task.task_type = 1 THEN 'TEXT'

            WHEN task.task_type = 2 THEN 'IMAGE'

            WHEN task.task_type = 3 THEN 'PDF'

            WHEN task.task_type = 4 THEN 'VIDEO'

            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE

            WHEN task.task_status = 0 THEN 'Pending'

            WHEN task.task_status = 1 THEN 'sent'

            WHEN task.task_status = 3 THEN 'Read'

            WHEN task.task_status = 2 THEN 'delivered'

            WHEN task.task_status = 4 THEN 'failed'

            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(88): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '1', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_name' in 'field list' at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select `task`.`...')
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `task`.`...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(88): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '1', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 {main}
"} 
[2025-06-03 12:02:45] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_name' in 'field list' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`template_name`, CASE
            WHEN task.task_type = 1 THEN 'TEXT'
            WHEN task.task_type = 2 THEN 'IMAGE'
            WHEN task.task_type = 3 THEN 'PDF'
            WHEN task.task_type = 4 THEN 'VIDEO'
            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE
            WHEN task.task_status = 0 THEN 'Pending'
            WHEN task.task_status = 1 THEN 'sent'
            WHEN task.task_status = 3 THEN 'Read'
            WHEN task.task_status = 2 THEN 'delivered'
            WHEN task.task_status = 4 THEN 'failed'
            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_name' in 'field list' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`template_name`, CASE

            WHEN task.task_type = 1 THEN 'TEXT'

            WHEN task.task_type = 2 THEN 'IMAGE'

            WHEN task.task_type = 3 THEN 'PDF'

            WHEN task.task_type = 4 THEN 'VIDEO'

            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE

            WHEN task.task_status = 0 THEN 'Pending'

            WHEN task.task_status = 1 THEN 'sent'

            WHEN task.task_status = 3 THEN 'Read'

            WHEN task.task_status = 2 THEN 'delivered'

            WHEN task.task_status = 4 THEN 'failed'

            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(88): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '2', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_name' in 'field list' at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select `task`.`...')
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `task`.`...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(88): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '2', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 {main}
"} 
[2025-06-03 12:03:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"A99uApoxy8VGvUy4pF5cAEH"}}  
[2025-06-03 12:03:44] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"At032aJNyZL1vWdsjgBFAkA"}}  
[2025-06-03 12:03:54] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_id' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`title` as `template_name`, CASE
            WHEN task.task_type = 1 THEN 'TEXT'
            WHEN task.task_type = 2 THEN 'IMAGE'
            WHEN task.task_type = 3 THEN 'PDF'
            WHEN task.task_type = 4 THEN 'VIDEO'
            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE
            WHEN task.task_status = 0 THEN 'Pending'
            WHEN task.task_status = 1 THEN 'sent'
            WHEN task.task_status = 3 THEN 'Read'
            WHEN task.task_status = 2 THEN 'delivered'
            WHEN task.task_status = 4 THEN 'failed'
            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` or `task`.`templateId` = `templates`.`template_id` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_id' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`title` as `template_name`, CASE

            WHEN task.task_type = 1 THEN 'TEXT'

            WHEN task.task_type = 2 THEN 'IMAGE'

            WHEN task.task_type = 3 THEN 'PDF'

            WHEN task.task_type = 4 THEN 'VIDEO'

            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE

            WHEN task.task_status = 0 THEN 'Pending'

            WHEN task.task_status = 1 THEN 'sent'

            WHEN task.task_status = 3 THEN 'Read'

            WHEN task.task_status = 2 THEN 'delivered'

            WHEN task.task_status = 4 THEN 'failed'

            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` or `task`.`templateId` = `templates`.`template_id` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(93): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '2', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_id' in 'on clause' at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select `task`.`...')
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `task`.`...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(93): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '2', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 {main}
"} 
[2025-06-03 12:03:54] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_id' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`title` as `template_name`, CASE
            WHEN task.task_type = 1 THEN 'TEXT'
            WHEN task.task_type = 2 THEN 'IMAGE'
            WHEN task.task_type = 3 THEN 'PDF'
            WHEN task.task_type = 4 THEN 'VIDEO'
            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE
            WHEN task.task_status = 0 THEN 'Pending'
            WHEN task.task_status = 1 THEN 'sent'
            WHEN task.task_status = 3 THEN 'Read'
            WHEN task.task_status = 2 THEN 'delivered'
            WHEN task.task_status = 4 THEN 'failed'
            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` or `task`.`templateId` = `templates`.`template_id` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_id' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`title` as `template_name`, CASE

            WHEN task.task_type = 1 THEN 'TEXT'

            WHEN task.task_type = 2 THEN 'IMAGE'

            WHEN task.task_type = 3 THEN 'PDF'

            WHEN task.task_type = 4 THEN 'VIDEO'

            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE

            WHEN task.task_status = 0 THEN 'Pending'

            WHEN task.task_status = 1 THEN 'sent'

            WHEN task.task_status = 3 THEN 'Read'

            WHEN task.task_status = 2 THEN 'delivered'

            WHEN task.task_status = 4 THEN 'failed'

            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` or `task`.`templateId` = `templates`.`template_id` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(93): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '1', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_id' in 'on clause' at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select `task`.`...')
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `task`.`...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(93): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '1', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 {main}
"} 
[2025-06-03 12:04:35] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column '' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`title` as `template_name`, CASE
            WHEN task.task_type = 1 THEN 'TEXT'
            WHEN task.task_type = 2 THEN 'IMAGE'
            WHEN task.task_type = 3 THEN 'PDF'
            WHEN task.task_type = 4 THEN 'VIDEO'
            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE
            WHEN task.task_status = 0 THEN 'Pending'
            WHEN task.task_status = 1 THEN 'sent'
            WHEN task.task_status = 3 THEN 'Read'
            WHEN task.task_status = 2 THEN 'delivered'
            WHEN task.task_status = 4 THEN 'failed'
            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` or `task`.`templateId` = `` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column '' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`title` as `template_name`, CASE

            WHEN task.task_type = 1 THEN 'TEXT'

            WHEN task.task_type = 2 THEN 'IMAGE'

            WHEN task.task_type = 3 THEN 'PDF'

            WHEN task.task_type = 4 THEN 'VIDEO'

            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE

            WHEN task.task_status = 0 THEN 'Pending'

            WHEN task.task_status = 1 THEN 'sent'

            WHEN task.task_status = 3 THEN 'Read'

            WHEN task.task_status = 2 THEN 'delivered'

            WHEN task.task_status = 4 THEN 'failed'

            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` or `task`.`templateId` = `` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(93): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '2', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column '' in 'on clause' at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select `task`.`...')
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `task`.`...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(93): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '2', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 {main}
"} 
[2025-06-03 12:04:35] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column '' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`title` as `template_name`, CASE
            WHEN task.task_type = 1 THEN 'TEXT'
            WHEN task.task_type = 2 THEN 'IMAGE'
            WHEN task.task_type = 3 THEN 'PDF'
            WHEN task.task_type = 4 THEN 'VIDEO'
            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE
            WHEN task.task_status = 0 THEN 'Pending'
            WHEN task.task_status = 1 THEN 'sent'
            WHEN task.task_status = 3 THEN 'Read'
            WHEN task.task_status = 2 THEN 'delivered'
            WHEN task.task_status = 4 THEN 'failed'
            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` or `task`.`templateId` = `` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column '' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`title` as `template_name`, CASE

            WHEN task.task_type = 1 THEN 'TEXT'

            WHEN task.task_type = 2 THEN 'IMAGE'

            WHEN task.task_type = 3 THEN 'PDF'

            WHEN task.task_type = 4 THEN 'VIDEO'

            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE

            WHEN task.task_status = 0 THEN 'Pending'

            WHEN task.task_status = 1 THEN 'sent'

            WHEN task.task_status = 3 THEN 'Read'

            WHEN task.task_status = 2 THEN 'delivered'

            WHEN task.task_status = 4 THEN 'failed'

            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` or `task`.`templateId` = `` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(93): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '1', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column '' in 'on clause' at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select `task`.`...')
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `task`.`...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(93): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '1', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 {main}
"} 
[2025-06-03 12:04:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AL1sLmHZ73_0jxyiloRxWNh"}}  
[2025-06-03 12:04:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"Axp1HnBZL7wXmj5POni18dz"}}  
[2025-06-03 12:04:46] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column '' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`title` as `template_name`, CASE
            WHEN task.task_type = 1 THEN 'TEXT'
            WHEN task.task_type = 2 THEN 'IMAGE'
            WHEN task.task_type = 3 THEN 'PDF'
            WHEN task.task_type = 4 THEN 'VIDEO'
            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE
            WHEN task.task_status = 0 THEN 'Pending'
            WHEN task.task_status = 1 THEN 'sent'
            WHEN task.task_status = 3 THEN 'Read'
            WHEN task.task_status = 2 THEN 'delivered'
            WHEN task.task_status = 4 THEN 'failed'
            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` or `task`.`templateId` = `` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column '' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`title` as `template_name`, CASE

            WHEN task.task_type = 1 THEN 'TEXT'

            WHEN task.task_type = 2 THEN 'IMAGE'

            WHEN task.task_type = 3 THEN 'PDF'

            WHEN task.task_type = 4 THEN 'VIDEO'

            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE

            WHEN task.task_status = 0 THEN 'Pending'

            WHEN task.task_status = 1 THEN 'sent'

            WHEN task.task_status = 3 THEN 'Read'

            WHEN task.task_status = 2 THEN 'delivered'

            WHEN task.task_status = 4 THEN 'failed'

            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` or `task`.`templateId` = `` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(93): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '2', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column '' in 'on clause' at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select `task`.`...')
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `task`.`...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(93): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '2', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 {main}
"} 
[2025-06-03 12:04:46] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column '' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`title` as `template_name`, CASE
            WHEN task.task_type = 1 THEN 'TEXT'
            WHEN task.task_type = 2 THEN 'IMAGE'
            WHEN task.task_type = 3 THEN 'PDF'
            WHEN task.task_type = 4 THEN 'VIDEO'
            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE
            WHEN task.task_status = 0 THEN 'Pending'
            WHEN task.task_status = 1 THEN 'sent'
            WHEN task.task_status = 3 THEN 'Read'
            WHEN task.task_status = 2 THEN 'delivered'
            WHEN task.task_status = 4 THEN 'failed'
            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` or `task`.`templateId` = `` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column '' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`title` as `template_name`, CASE

            WHEN task.task_type = 1 THEN 'TEXT'

            WHEN task.task_type = 2 THEN 'IMAGE'

            WHEN task.task_type = 3 THEN 'PDF'

            WHEN task.task_type = 4 THEN 'VIDEO'

            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE

            WHEN task.task_status = 0 THEN 'Pending'

            WHEN task.task_status = 1 THEN 'sent'

            WHEN task.task_status = 3 THEN 'Read'

            WHEN task.task_status = 2 THEN 'delivered'

            WHEN task.task_status = 4 THEN 'failed'

            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` or `task`.`templateId` = `` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(93): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '1', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column '' in 'on clause' at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select `task`.`...')
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `task`.`...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(93): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '1', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 {main}
"} 
[2025-06-03 12:05:31] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_id' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`title` as `template_name`, CASE
            WHEN task.task_type = 1 THEN 'TEXT'
            WHEN task.task_type = 2 THEN 'IMAGE'
            WHEN task.task_type = 3 THEN 'PDF'
            WHEN task.task_type = 4 THEN 'VIDEO'
            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE
            WHEN task.task_status = 0 THEN 'Pending'
            WHEN task.task_status = 1 THEN 'sent'
            WHEN task.task_status = 3 THEN 'Read'
            WHEN task.task_status = 2 THEN 'delivered'
            WHEN task.task_status = 4 THEN 'failed'
            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` or `task`.`templateId` = `templates`.`template_id` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_id' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`title` as `template_name`, CASE

            WHEN task.task_type = 1 THEN 'TEXT'

            WHEN task.task_type = 2 THEN 'IMAGE'

            WHEN task.task_type = 3 THEN 'PDF'

            WHEN task.task_type = 4 THEN 'VIDEO'

            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE

            WHEN task.task_status = 0 THEN 'Pending'

            WHEN task.task_status = 1 THEN 'sent'

            WHEN task.task_status = 3 THEN 'Read'

            WHEN task.task_status = 2 THEN 'delivered'

            WHEN task.task_status = 4 THEN 'failed'

            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` or `task`.`templateId` = `templates`.`template_id` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(93): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '2', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_id' in 'on clause' at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select `task`.`...')
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `task`.`...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(93): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '2', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 {main}
"} 
[2025-06-03 12:05:31] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_id' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`title` as `template_name`, CASE
            WHEN task.task_type = 1 THEN 'TEXT'
            WHEN task.task_type = 2 THEN 'IMAGE'
            WHEN task.task_type = 3 THEN 'PDF'
            WHEN task.task_type = 4 THEN 'VIDEO'
            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE
            WHEN task.task_status = 0 THEN 'Pending'
            WHEN task.task_status = 1 THEN 'sent'
            WHEN task.task_status = 3 THEN 'Read'
            WHEN task.task_status = 2 THEN 'delivered'
            WHEN task.task_status = 4 THEN 'failed'
            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` or `task`.`templateId` = `templates`.`template_id` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_id' in 'on clause' (Connection: mysql, SQL: select `task`.`task_id`, `task`.`campaign_name`, `devices`.`name` as `name`, `task`.`launched_on`, `task`.`scheduled_on`, `task`.`whatsapp_sent_time`, `task`.`whatsapp_received_time`, `task`.`whatsapp_read_time`, `task`.`send_to_number`, `task`.`templateId`, `task`.`task_description`, `templates`.`title` as `template_name`, CASE

            WHEN task.task_type = 1 THEN 'TEXT'

            WHEN task.task_type = 2 THEN 'IMAGE'

            WHEN task.task_type = 3 THEN 'PDF'

            WHEN task.task_type = 4 THEN 'VIDEO'

            ELSE 'UNKNOWN' END as task_type, `task`.`text`, `task`.`task_url`, CASE

            WHEN task.task_status = 0 THEN 'Pending'

            WHEN task.task_status = 1 THEN 'sent'

            WHEN task.task_status = 3 THEN 'Read'

            WHEN task.task_status = 2 THEN 'delivered'

            WHEN task.task_status = 4 THEN 'failed'

            ELSE 'UNKNOWN' END as status_text from `task` inner join `devices` on `task`.`device_id` = `devices`.`id` left join `templates` on `task`.`templateId` = `templates`.`id` or `task`.`templateId` = `templates`.`template_id` where `task`.`created_by` = 2 and date(`scheduled_on`) >= 2025-06-03 and date(`scheduled_on`) <= 2025-06-03 order by `task`.`task_id` desc limit 10 offset 0) at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(93): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '1', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'templates.template_id' in 'on clause' at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select `task`.`...')
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `task`.`...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select `task`.`...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select `task`.`...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select `task`.`...', Array, true)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php(93): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\ReportController.php(96): App\\Models\\Report->get_data('2025-06-03', '2025-06-03', '1', '0', '10', NULL, 'whatsapp_read_t...', 'desc', NULL, NULL)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\ReportController->get_data_filter(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('get_data_filter', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\ReportController), 'get_data_filter')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 {main}
"} 
[2025-06-03 12:05:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AbiSZWxfxmU1UY3IUBb5vDy"}}  
[2025-06-03 12:05:45] local.ERROR: syntax error, unexpected token "}" {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected token \"}\" at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php:43)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:05:50] local.ERROR: syntax error, unexpected token "}" {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected token \"}\" at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Models\\Report.php:43)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:06:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"Aitkaz3T8KgL32bFFerhgLX"}}  
[2025-06-03 12:06:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AJ2vuK2cMx5rV_OiMBZ2prF"}}  
[2025-06-03 12:07:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AXOpDHh1YPMaY9Qv8DCnBFY"}}  
[2025-06-03 12:07:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"A9sInQgaRoLc-TJcFK-TTTn"}}  
[2025-06-03 12:08:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AA67VcGKP7hKxzgAU6QNOxs"}}  
[2025-06-03 12:08:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"ARy7dD2AtiMCVqTBdbO2IHy"}}  
[2025-06-03 12:09:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"A-x4qsaXd7ZcZ98O6RN1_hH"}}  
[2025-06-03 12:09:43] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"ApCOXw_Pf1LDvr9WeIiEOs2"}}  
