
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => 'Facebook Leads',
        'buttons' => array_filter([
            [
                'name' => '<i class="fa fa-plus"></i>&nbsp' . __('Add Integration'),
                'url' => route('user.facebook_leads.create'),
            ],
        ]),
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/select2/dist/css/select2.min.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row justify-content-center">
        <div class="col-12">
            <?php if(api_plan() && api_plan()->title == 'Api'): ?>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="alert bg-gradient-primary text-white alert-dismissible fade show" role="alert">
                            <span class="alert-icon"><i class="fi  fi-rs-info text-white"></i></span>
                            <span class="alert-text">
                                <strong><?php echo e(__('!Opps ')); ?></strong>

                                <?php echo e(__('Facebook integration features is not available in your subscription plan')); ?>


                            </span>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            

            <?php if(count($fbLeadData ?? []) == 0): ?>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <center>
                                    <img src="<?php echo e(asset('assets/img/404.jpg')); ?>" height="500">
                                    <h3 class="text-center"><?php echo e(__('!Opps You Have Not Created Facebook Leads')); ?></h3>
                                    <a href="<?php echo e(route('user.facebook_leads.create')); ?>" class="btn btn-neutral"><i
                                            class="fas fa-plus"></i>
                                        <?php echo e(__('Create new lead')); ?></a>
                                </center>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-header border-0">
                        <h3 class="mb-0"><?php echo e(__('Leads List')); ?></h3>
                        
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-12 table-responsive">
                                <table class="table col-12">
                                    <thead>
                                        <tr>
                                            
                                            <th><?php echo e(__('Device')); ?></th>
                                            <th><?php echo e(__('Page Name')); ?></th>
                                            <th><?php echo e(__('Form Name')); ?></th>
                                            <th><?php echo e(__('Template')); ?></th>
                                            <th><?php echo e(__('Tag')); ?></th>
                                            <th class="text-right"><?php echo e(__('Action')); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody class="tbody">
                                        <?php $__currentLoopData = $fbLeadData ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            
                                            <tr>
                                                
                                                <td><?php echo e($fb->device->name); ?> <br>
                                                    
                                                    (+<?php echo e($fb->device->phone ?? ''); ?>)
                                                </td>

                                                <td><?php echo e($fb->page_name); ?></td>
                                                <td><?php echo e($fb->form_name); ?></td>
                                                <td>
                                                    <?php echo e($fb->template); ?> (<?php echo e($fb->tmp_lang); ?>)
                                                </td>
                                                <td><?php echo e($fb->tag->tag_name ?? ''); ?></td>

                                                <td>
                                                    <a class="dropdown-item has-icon delete-confirm"
                                                        href="javascript:void(0)"
                                                        data-action="<?php echo e(route('user.facebook_leads.destroy', $fb->id)); ?>"><i
                                                            class="fas fa-trash text-danger float-right"></i></a>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                                
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/vendor/select2/dist/js/select2.min.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/facebook_leads/list.blade.php ENDPATH**/ ?>