
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', ['title' => __('Create Drip Campaign')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/select2/dist/css/select2.min.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12 col-sm-12 col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h4><?php echo e(__('Create Drip Campaign')); ?></h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 col-sm-12 col-md-12">
                            <div class="tab-content no-padding" id="myTab2Content">
                                <div class="tab-pane fade show active" id="home4" role="tabpanel"
                                    aria-labelledby="home-tab4">
                                    <form method="POST" action="<?php echo e(route('user.drip_campaign.store')); ?>" class="ajaxform">
                                        <?php echo csrf_field(); ?>
                                        <div class="row">
                                            <div class="form-group col-md-6">
                                                <!-- Drip Name field -->
                                                <label for="drip_name"><?php echo e(__('Drip Name')); ?></label>
                                                <input type="text" class="form-control" name="drip_name" id="drip_name"
                                                    placeholder="Drip Name">
                                            </div>
                                            <div class="form-group col-md-6">
                                                <!-- Select Device Dropdown field -->
                                                <label for="device_name"><?php echo e(__('Select Device')); ?></label>
                                                <select class="form-control" id="device_id" name="device_id" required
                                                    data-toggle="select">
                                                    <option value=""><?php echo e(__('-- Select Device --')); ?></option>
                                                    <?php $__currentLoopData = $devices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($device['id']); ?>"><?php echo e($device['name']); ?>

                                                            (+<?php echo e($device['phone']); ?>)
                                                        </option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                        <hr>

                                        <div id="dripFieldsContainer">
                                            <div class="row drip-row"
                                                style="border:1px solid #ccc; padding: 5px; margin: 5px;border-radius: 5px;">
                                                <div class="form-group col-md-2">
                                                    <label for="drip_count"><?php echo e(__('Drip')); ?></label>
                                                    <input type="text" class="form-control drip-count-input"
                                                        name="drip_count[]" id="drip_count" value="Drip 1" readonly>
                                                </div>
                                                <div class="form-group col-md-1">
                                                    <label for="days"><?php echo e(__('Days')); ?></label>
                                                    <input type="text" class="form-control" name="days[]" id="days"
                                                        placeholder="Days" min="1" max="60" required>
                                                </div>
                                                <div class="form-group col-md-4">
                                                    <label for="drip_template"><?php echo e(__('Template')); ?></label>
                                                    <select class="form-control" id="drip_template" name="drip_template[]"
                                                        required="" data-toggle="select">
                                                    </select>
                                                </div>

                                                <div class="form-group col-md-4" id="b_msg_media" style="display: none;">
                                                    <label for="media_file"><?php echo e(__('Media')); ?></label>
                                                    <input id="media_file" type="file" class="form-control"
                                                        name="media_file[]" />
                                                    <small><?php echo e(__(' Supported file type:')); ?></small> <small
                                                        class="text-danger"><?php echo e(__('( jpg, png, pdf, mp4, jpeg )')); ?></small>
                                                </div>
                                                <div class="form-group col-md-1" id="b_temp_langue" style="display: none;">
                                                    <label for="b_language"><?php echo e(__('Language')); ?></label>
                                                    <input type="text" class="form-control" name="b_language[]"
                                                        id="b_language" placeholder="Language">
                                                </div>
                                                <div class="form-group col-md-3" id="b_tsk_type" style="display: none;">
                                                    <label for="b_task_type"><?php echo e(__('Task Type')); ?></label>
                                                    <input type="text" class="form-control" name="b_task_type[]"
                                                        id="b_task_type" placeholder="Task Type">
                                                </div>
                                                <div class="form-group col-md-3" id="check_media" style="display: none;">
                                                    <label for="has_media"><?php echo e(__('Birthday Media')); ?></label>
                                                    <input type="textarea" class="form-control" name="has_media[]"
                                                        id="has_media" value="1">
                                                </div>
                                                <div class="form-group col-md-1">
                                                    
                                                    <label>&nbsp;</label>
                                                    <div>
                                                        <button type="button" class="btn btn-neutral btn-md add_button"
                                                            href="javascript:void(0);">
                                                            <i class="fa fa-plus"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <button
                                                class="btn btn-neutral submit-button float-right"><?php echo e(__('Create')); ?></button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/vendor/select2/dist/js/select2.min.js')); ?>"></script>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function() {
            // document.getElementById('drip_template').innerText = null;

            $(document).ready(function() {
                function selectSingleDevice() {
                    var deviceDropdown = $("#device_id");
                    if (deviceDropdown.children("option").length == 2) { // One option is the placeholder
                        deviceDropdown.children("option").eq(1).prop('selected', true);
                        deviceDropdown.change();
                    }
                }
                selectSingleDevice();
            });

            $("#device_id").change(function() {
                var selectedDevice = $(this).val();
                $('#drip_template').empty().append(
                    '<option value="" >-- Select Template --</option>');

                if (!selectedDevice) return;

                $.ajax({
                    url: "<?php echo e(route('user.get_templates', ':device_id')); ?>".replace(':device_id',
                        selectedDevice),
                    type: 'GET',
                    success: function(templates) {
                        $.each(templates, function(index, value) {
                            if (value.status == "1") {
                                $('#drip_template').append("<option value='" + value
                                    .id +
                                    "'>" + value.title + " -- " + value.type +
                                    "</option>");
                            }
                        });
                    },
                    error: function(xhr) {
                        console.error('AJAX error:', xhr.responseText);
                    }
                });

            });

            const MAX_DRIPS = 5;
            updateDripCounts();
            updateAddButtonVisibility();

            // Add new row
            $('.add_button').on('click', function() {

                if ($('.drip-row').length >= MAX_DRIPS) {
                    // Optional: Show message to user
                    alert('Maximum ' + MAX_DRIPS + ' drips allowed');
                    return;
                }
                var nextDripNumber = $('.drip-row').length + 1;

                // Get the current device ID
                var deviceId = $("#device_id").val();
                var deviceData = <?php echo json_encode($devices, 15, 512) ?>;

                // Create new row
                var newRow = `
                            <div class="row drip-row" style="border:1px solid #ccc; padding: 5px; margin: 5px;border-radius: 5px;">
                                <div class="form-group col-md-2">
                                    <label><?php echo e(__('Drip')); ?></label>
                                    <input type="text" class="form-control drip-count-input" name="drip_count[]" value="Drip ${nextDripNumber}" readonly>
                                </div>
                                <div class="form-group col-md-1">
                                    <label><?php echo e(__('Days')); ?></label>
                                    <input type="text" class="form-control" name="days[]" placeholder="Days" min="1" max="60" required>
                                </div>
                                <div class="form-group col-md-4">
                                    <label><?php echo e(__('Template')); ?></label>
                                    <select class="form-control template-select" name="drip_template[]" required>
                                        <option value=""><?php echo e(__('-- Select Template --')); ?></option>
                                    </select>
                                </div>
                                <div class="form-group col-md-4 media-section" style="display: none;">
                                    <label><?php echo e(__('Media')); ?></label>
                                    <input type="file" class="form-control media-file" name="media_file[]">
                                    <small><?php echo e(__('Supported file type:')); ?></small>
                                    <small class="text-danger"><?php echo e(__('( jpg, png, pdf, mp4, jpeg )')); ?></small>
                                </div>
                                <input type="hidden" class="language-input" name="b_language[]">
                                <input type="hidden" class="has-media-input" name="has_media[]" value="0">
                                <input type="hidden" class="task-type-input" name="b_task_type[]" value="1">
                                <div class="form-group col-md-1">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn btn-danger btn-md remove-row">
                                            <i class="fa fa-minus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;

                // Append new row
                $('#dripFieldsContainer').append(newRow);

                updateDripCounts();
                updateAddButtonVisibility();

                // Populate template dropdown for new row
                var $lastRow = $('.drip-row:last');
                populateTemplateDropdown($lastRow, deviceId, deviceData);

                // Initialize template change handler for new row
                // initializeTemplateHandler($lastRow);
            });

            // Remove row
            $(document).on('click', '.remove-row', function() {
                $(this).closest('.drip-row').remove();
                updateDripCounts();
                updateAddButtonVisibility();
            });

            // Function to update all drip counts
            function updateDripCounts() {
                $('.drip-row').each(function(index) {
                    $(this).find('.drip-count-input').val('Drip ' + (index + 1));
                });
            }

            function updateAddButtonVisibility() {
                if ($('.drip-row').length >= MAX_DRIPS) {
                    $('.add_button').hide();
                    // Optional: Add a message or disabled state
                    // $('.add_button').after('<small class="text-danger">Maximum limit reached</small>');
                } else {
                    $('.add_button').show();
                    // Optional: Remove the message if it exists
                    // $('.add_button').next('small').remove();
                }
            }

            function populateTemplateDropdown($row, deviceId, deviceData) {
                console.log('device data is ', deviceData);
                console.log('device id is ', deviceId);
                var $templateSelect = $row.find('.template-select');
                $templateSelect.empty().append('<option value="-1">-- Select Template --</option>');

                $.ajax({
                    url: "<?php echo e(route('user.get_templates', ':device_id')); ?>".replace(':device_id',
                        deviceId),
                    type: 'GET',
                    success: function(templates) {
                        $.each(templates, function(index, value) {
                            if (value.status == "1") {
                                $templateSelect.append("<option value='" + value
                                    .id +
                                    "'>" + value.title + " -- " + value.type +
                                    "</option>");
                            }
                        });
                    },
                    error: function(xhr) {
                        console.error('AJAX error:', xhr.responseText);
                    }
                });

            }


            $('.drip-row').each(function() {
                // initializeTemplateHandler($(this));
            })

            function initializeTemplateHandler($row) {
                $row.find('.template-select').on('change', function() {
                    var deviceId = $("#device_id").val();
                    var templateName = $(this).val();
                    var deviceData = <?php echo json_encode($devices, 15, 512) ?>;

                    if (deviceId === '-1' || templateName === '-1') return;

                    var template = deviceData[deviceId].templates.find(t => t.name === templateName);
                    if (!template) return;

                    var $mediaSection = $row.find('.media-section');
                    var $mediaFile = $row.find('.media-file');
                    var $hasMediaInput = $row.find('.has-media-input');
                    var $taskTypeInput = $row.find('.task-type-input');
                    var $languageInput = $row.find('.language-input');

                    // Set language
                    $languageInput.val(template.language);

                    // Handle different template types
                    var format = template.components[0].format;
                    switch (format) {
                        case 'IMAGE':
                            setupMediaField($mediaSection, $mediaFile, $hasMediaInput, $taskTypeInput, true,
                                '1', '2');
                            break;
                        case 'DOCUMENT':
                            setupMediaField($mediaSection, $mediaFile, $hasMediaInput, $taskTypeInput, true,
                                '1', '3');
                            break;
                        case 'VIDEO':
                            setupMediaField($mediaSection, $mediaFile, $hasMediaInput, $taskTypeInput, true,
                                '1', '4');
                            break;
                        case 'TEXT':
                            setupMediaField($mediaSection, $mediaFile, $hasMediaInput, $taskTypeInput,
                                false, '0', '1');
                            break;
                        default:
                            setupMediaField($mediaSection, $mediaFile, $hasMediaInput, $taskTypeInput,
                                false, '0', '1');
                    }
                });
            }

            function setupMediaField($mediaSection, $mediaFile, $hasMediaInput, $taskTypeInput, showMedia, hasMedia,
                taskType) {
                if (showMedia) {
                    $mediaSection.show();
                    $mediaFile.prop('disabled', false);
                } else {
                    $mediaSection.hide();
                    $mediaFile.prop('disabled', true);
                }

                $hasMediaInput.val(hasMedia);
                $taskTypeInput.val(taskType);
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/drip_campaign/create.blade.php ENDPATH**/ ?>