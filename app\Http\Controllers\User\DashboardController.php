<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Device;
use App\Models\Smstransaction;
use App\Models\Schedulemessage;
use App\Models\Contact;
use App\Models\User;
use App\Models\Task;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;

class DashboardController extends Controller
{
    //defind user_id publically
    public $user_id;

    public function __construct()
    {
        $this->middleware('auth');

        $this->middleware(function ($request, $next) {
            if (Auth::user() && Auth::user()->parent) {
                $this->user_id = Auth::user()->parent->id;
            } else {
                $this->user_id = Auth::id();
            }

            return $next($request);
        });
    }

    public function index()
    {
        $user_id = $this->user_id;
        $user = Cache::remember("user_{$user_id}_" . session()->getId(), now()->addMinutes(10), function () use ($user_id) {
            return User::find($user_id);
        });

        if ($user->will_expire != null) {
            $nextDate = Carbon::now()->addDays(7)->format('Y-m-d');
            if ($user->will_expire <= now()) {
                Session::flash('saas_error', __('Your subscription was expired at ' . Carbon::parse($user->will_expire)->diffForHumans() . ' please renew the subscription'));
            } elseif ($user->will_expire <= $nextDate) {
                Session::flash('saas_error', __('Your subscription is ending in ' . Carbon::parse($user->will_expire)->diffForHumans()));
            }
        }

        return view('user.dashboard');
    }

    public function dashboardData()
    {
        //dd auth data
        $authId = $this->user_id;

        $data['totalBalance'] = User::where('id', $authId)->value('balance');
        $data['messageRate'] = User::where('id', $authId)->value('business_initiated');
        $data['devicesCount'] = Device::where('user_id', $authId)->count();
        $data['messagesCount'] = Task::where('created_by', $authId)->count();
        $data['contactCount'] = Contact::where('user_id', $authId)->count();
        $data['pendingCount'] = Task::where('task_status', 0)->where('created_by', $authId)->count();

        $data['devices'] = Device::where('user_id', $authId)
            ->withCount('tasks')
            ->orderBy('status', 'DESC')
            ->latest()
            ->get()
            ->map(function ($rq) {
                $map['uuid'] = $rq->uuid;
                $map['name'] = $rq->name;
                $map['status'] = $rq->status;
                // $map['quality_rating'] = $rq->quality_rating;
                $map['phone'] = $rq->phone;
                $map['tasks_count'] = $rq->tasks_count;
                return $map;
            });
        $data['messagesStatics'] = $this->getMessagesTransaction(7);
        $data['typeStatics'] = $this->messagesStatics(7);
        // $data['chatbotStatics'] = $this->getChatbotTransaction(7);


        return response()->json($data);
    }

    public function getMessagesTransaction($days)
    {
        $fromDate = Carbon::now()->subDays($days)->startOfDay();
        $toDate = Carbon::now()->endOfDay();
        $statics = Task::where('created_by', $this->user_id)
            ->whereBetween('scheduled_on', [$fromDate, $toDate])
            ->selectRaw('date(scheduled_on) date, COUNT(*) task')
            ->groupBy('date')
            ->get();
        return $statics;
    }

    // public function getChatbotTransaction($days)
    // {
    //     $fromDate = Carbon::now()->subDays($days)->startOfDay();
    //     $toDate = Carbon::now()->endOfDay();
    //     $statics = Task::where('created_by', $this->user_id)
    //         ->where('templateId', 'auto_reply')
    //         ->whereBetween('scheduled_on', [$fromDate, $toDate])
    //         ->selectRaw('date(scheduled_on) date, COUNT(*) task')
    //         ->groupBy('date')
    //         ->get();
    //     return $statics;
    // }

    public function messagesStatics($days)
    {
        $fromDate = Carbon::now()->subDays($days)->startOfDay();
        $toDate = Carbon::now()->endOfDay();
        $statics = Task::where('created_by', $this->user_id)
            ->whereBetween('scheduled_on', [$fromDate, $toDate])
            ->selectRaw('CASE 
        WHEN task_status = 0 THEN "Pending" 
        WHEN task_status = 1 THEN "Sent" 
        WHEN task_status = 2 THEN "Delivered"
        WHEN task_status = 3 THEN "Read"
        WHEN task_status = 4 THEN "Failed"
        ELSE "Unknown" 
        END as type, count(*) as task')
            ->groupBy('task_status')
            ->get();

        return $statics;
    }
}
