<!-- Nav items -->
<ul class="navbar-nav">
    <?php if(api_plan() && api_plan()->title == 'Api'): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/dashboard*') ? 'active' : ''); ?>"
                href="<?php echo e(route('user.dashboard.index')); ?>">
                <i class="fi fi-rs-apps"></i>
                <span class="nav-link-text"><?php echo e(__('Dashboard')); ?></span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/device*') ? 'active' : ''); ?>" href="<?php echo e(route('user.account.index')); ?>">
                <i class="fi-rs-devices"></i>
                <span class="nav-link-text"><?php echo e(__('My Devices')); ?></span>
            </a>
        </li>

        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/apps*') ? 'active' : ''); ?>" href="<?php echo e(route('user.apps.index')); ?>">
                <i class="fi fi-rs-apps-add"></i>
                <span class="nav-link-text"><?php echo e(__('My Api')); ?></span>
            </a>
        </li>

        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/template*') ? 'active' : ''); ?>" href="<?php echo e(url('user/template')); ?>">
                <i class="fi fi-rs-form"></i>
                <span class="nav-link-text"><?php echo e(__('My Templates')); ?></span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/response*') ? 'active' : ''); ?>" href="<?php echo e(url('user/response')); ?>">
                <i class="fi-rs-message-code"></i>
                <span class="nav-link-text"><?php echo e(__('Response')); ?></span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/report*') ? 'active' : ''); ?>" href="<?php echo e(url('user/report')); ?>">
                <i class="fi fi-rs-document-signed"></i>
                <span class="nav-link-text"><?php echo e(__('Report')); ?></span>
            </a>
        </li>
    <?php else: ?>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/dashboard*') ? 'active' : ''); ?>"
                href="<?php echo e(route('user.dashboard.index')); ?>">
                <i class="fi fi-rs-apps"></i>
                <span class="nav-link-text"><?php echo e(__('Dashboard')); ?></span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/account*') ? 'active' : ''); ?>"
                href="<?php echo e(route('user.account.index')); ?>">
                <i class="fi-rs-devices"></i>
                <span class="nav-link-text"><?php echo e(__('My Account')); ?></span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/sent-text-message*') ? 'active' : ''); ?>"
                href="<?php echo e(url('user/sent-text-message')); ?>">
                <i class="fi fi-rs-paper-plane"></i>
                <span class="nav-link-text"><?php echo e(__('Send Message')); ?></span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/dynamic-sent-text-message*') ? 'active' : ''); ?>"
                href="<?php echo e(url('user/dynamic-sent-text-message')); ?>">
                <i class="fi fi-rs-paper-plane"></i>
                <span class="nav-link-text"><?php echo e(__('Send Dynamic Message')); ?></span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/chatbot*') ? 'active' : ''); ?>"
                href="<?php echo e(route('user.chatbot.index')); ?>">
                <i class="fi fi-rs-comment-alt"></i>
                <span class="nav-link-text"><?php echo e(__('Auto Reply')); ?></span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/apps*') ? 'active' : ''); ?>" href="<?php echo e(route('user.apps.index')); ?>">
                <i class="fi fi-rs-apps-add"></i>
                <span class="nav-link-text"><?php echo e(__('My Api')); ?></span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/contact*') ? 'active' : ''); ?>"
                href="<?php echo e(route('user.contact.index')); ?>">
                <i class="fi  fi-rs-address-book"></i>
                <span class="nav-link-text"><?php echo e(__('Contacts Book')); ?></span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/template*') ? 'active' : ''); ?>" href="<?php echo e(url('user/template')); ?>">
                <i class="fi fi-rs-form"></i>
                <span class="nav-link-text"><?php echo e(__('My Templates')); ?></span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/response*') ? 'active' : ''); ?>" href="<?php echo e(url('user/response')); ?>">
                <i class="fi-rs-message-code"></i>
                <span class="nav-link-text"><?php echo e(__('Response')); ?></span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/report*') ? 'active' : ''); ?>" href="<?php echo e(url('user/report')); ?>">
                <i class="fi fi-rs-document-signed"></i>
                <span class="nav-link-text"><?php echo e(__('Report')); ?></span>
            </a>
        </li>
        <li class="nav-item ">
            <a class="nav-link <?php echo e(Request::is('user/greetings*') ? 'active' : ''); ?>"
                href="<?php echo e(url('user/greetings')); ?>">
                <i class="fi fi-rs-gifts"></i>
                <span class="nav-link-text"><?php echo e(__('Greetings')); ?></span>
            </a>
        </li>
        <li class="nav-item ">
            <a class="nav-link <?php echo e(Request::is('user/tag*') ? 'active' : ''); ?>" href="<?php echo e(url('user/tag')); ?>">
                <i class="fi fi-rs-tags"></i>
                <span class="nav-link-text"><?php echo e(__('Manage Tag')); ?></span>
            </a>
        </li>
        <li class="nav-item ">
            <a class="nav-link <?php echo e(Request::is('user/facebook_leads*') ? 'active' : ''); ?>"
                href="<?php echo e(url('user/facebook_leads')); ?>">
                <i class="fi fi-rs-shopping-basket"></i>
                <span class="nav-link-text"><?php echo e(__('Integration')); ?></span>
            </a>
        </li>
        <li class="nav-item ">
            <a class="nav-link <?php echo e(Request::is('user/drip_campaign*') ? 'active' : ''); ?>"
                href="<?php echo e(route('user.drip_campaign.index')); ?>">
                <i class="fi fi-rs-raindrops"></i>
                <span class="nav-link-text"><?php echo e(__('Drip Campaign')); ?></span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/bot_builder*') ? 'active' : ''); ?>"
                href="<?php echo e(route('user.bot_builder.index')); ?>">
                <i class="fas fa-robot"></i>
                <span class="nav-link-text"><?php echo e(__('Bot Builder')); ?></span>
                <!-- Add the "New" label here -->
                <span class="badge badge-pill badge-danger ml-2">New</span>
            </a>
        </li>
    <?php endif; ?>
</ul>


<!-- Divider -->
<hr class="my-3 mt-6">
<!-- Heading -->
<h6 class="navbar-heading p-0 text-muted"><?php echo e(__('Settings')); ?></h6>
<!-- Navigation -->
<ul class="navbar-nav mb-md-3">
    <?php
        $currentDomain = request()->getHttpHost();
        $allowedDomain = 'waba.nxccontrols.in';
    ?>
    
    
    <li class="nav-item">
        <a class="nav-link <?php echo e(Request::is('user/agent*') ? 'active' : ''); ?>" href="<?php echo e(route('user.agent.index')); ?>">
            <i class="fi fi-rs-shield-check"></i>
            <span class="nav-link-text"><?php echo e(__('Agent')); ?></span>
            <span class="badge badge-pill badge-danger ml-2">New</span>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link <?php echo e(Request::is('user/deletechat*') ? 'active' : ''); ?>"
            href="<?php echo e(url('/user/deletechat')); ?>">
            <i class="fi fi-rs-trash"></i>
            <span class="nav-link-text"><?php echo e(__('Delete Chat')); ?></span>
        </a>
    </li>
    <?php if($currentDomain == $allowedDomain): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/subscription*') ? 'active' : ''); ?>"
                href="<?php echo e(url('/user/subscription')); ?>">
                <i class="fi fi-rs-rocket-lunch"></i>
                <span class="nav-link-text"><?php echo e(__('Subscription')); ?></span>
            </a>
        </li>
    <?php else: ?>
        <li class="nav-item">
            <a class="nav-link <?php echo e(Request::is('user/subscriptions/log') ? 'active' : ''); ?>"
                href="<?php echo e(url('/user/subscriptions/log')); ?>">
                <i class="fi fi-rs-rocket-lunch"></i>
                <span class="nav-link-text"><?php echo e(__('Subscription Log')); ?></span>
            </a>
        </li>
    <?php endif; ?>
    <li class="nav-item">
        <a class="nav-link <?php echo e(Request::is('user/support*') ? 'active' : ''); ?>" href="<?php echo e(url('/user/support')); ?>">
            <i class="fi fi-rs-headset"></i>
            <span class="nav-link-text"><?php echo e(__('Help & Support')); ?></span>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="<?php echo e(url('/user/profile')); ?>">
            <i class="fi fi-rs-settings"></i>
            <span class="nav-link-text"><?php echo e(__('Profile Settings')); ?></span>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link <?php echo e(Request::is('user/auth-key*') ? 'active' : ''); ?>" href="<?php echo e(url('/user/auth-key')); ?>">
            <i class="fi fi-rs-key"></i>
            <span class="nav-link-text"><?php echo e(__('Auth Key')); ?></span>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link logout-button" href="#">
            <i class="fi fi-rs-log-out"></i>
            <span class="nav-link-text"><?php echo e(__('Logout')); ?></span>
        </a>
    </li>
</ul>
<?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/layouts/main/user.blade.php ENDPATH**/ ?>