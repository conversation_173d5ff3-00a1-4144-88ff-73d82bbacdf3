@extends('layouts.main.app')
@section('head')
    @include('layouts.main.headersection')
@endsection
@push('topcss')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/css/uikit.min.css">
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/vendor/select2/dist/css/select2.min.css') }}">
@endpush
@section('content')
    <div class="row justify-content-center">
        <div class="col-12">
            @if (api_plan() && api_plan()->title == 'Api')
                <div class="row">
                    <div class="col-sm-12">
                        <div class="alert bg-gradient-primary text-white alert-dismissible fade show" role="alert">
                            <span class="alert-icon"><i class="fi  fi-rs-info text-white"></i></span>
                            <span class="alert-text">
                                <strong>{{ __('!Opps ') }}</strong>
                                {{ __('Indiamart integration features is not available in your subscription plan') }}
                            </span>
                        </div>
                    </div>
                </div>
            @endif
            <div class="card">
                <div class="card-header">
                    <h4>{{ __('Indiamart Integration') }}</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 col-sm-12 col-md-12">
                            <div class="tab-content no-padding" id="myTab2Content">
                                <div class="tab-pane fade show active" id="mode_1" role="tabpanel"
                                    aria-labelledby="home-tab4">
                                    <form method="POST" action="{{ route('user.account.indMartIntStore') }}"
                                        class="ajaxform_reset_form" enctype="multipart/form-data">
                                        @csrf
                                        <div class="row">
                                            <div class="col-sm-12  mb-4">
                                                <label>{{ __('Device Name') }}</label>
                                                <input type="text" value="{{ $device->name }} (+{{ $device->phone }})"
                                                    class="form-control" readonly>
                                            </div>
                                            <div class="col-sm-12 mb-4" style="display: none;">
                                                <label>{{ __('Device Id') }}</label>
                                                <input type="text" value="{{ $device->id }}" name="device_id"
                                                    id="device_id" class="form-control" readonly>
                                            </div>
                                            {{-- <div class="col-sm-12  mb-4" style="display: none;">
                                                <label>{{ __('Select Device') }}</label>
                                                <select class="form-control" name="device_id" id="device_id"
                                                    data-toggle="select">
                                                    <option value="-1">{{ __('-- Select Device --') }}</option>
                                                    @foreach ($device as $dvc)
                                                        <option value="{{ $dvc->id }}">{{ $dvc['name'] }}
                                                            (+{{ $device['phone'] }})
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div> --}}
                                            <div class="col-sm-12  mb-4">
                                                <label>{{ __('Select Message Template') }}</label>
                                                <select class="form-control" name="msg_template" id="msg_template"
                                                    data-toggle="select">
                                                </select>
                                            </div>
                                            <div class="col-sm-12 mb-4">
                                                <label>{{ __('Indiamart Key') }}</label>
                                                <textarea name="im_key" id="im_key" cols="30" rows="2" class="form-control"></textarea>
                                            </div>
                                            <div class="col-sm-12">
                                                <div class="col-sm-12">
                                                    {{-- <button type="submit"
                                                        class="btn btn-outline-danger submit-button float-left">{{ __('Delete Integration') }}</button> --}}
                                                    {{-- @if (!empty(Auth::user()->fb_access_token)) --}}
                                                    <a href="{{ route('user.deleteImIntegration', $device['uuid']) }}"
                                                        id="delete_im_integration" style="display: none;"
                                                        class="btn btn-outline-danger float-left">{{ __('Delete Integration') }}
                                                    </a>
                                                    {{-- @endif --}}
                                                    <button type="submit"
                                                        class="btn btn-outline-primary submit-button float-right">{{ __('Save') }}</button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/vendor/select2/dist/js/select2.min.js') }}"></script>
    <script src="https://woody180.github.io/vanilla-javascript-emoji-picker/vanillaEmojiPicker.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/js/uikit.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/js/uikit-icons.min.js"></script>

    <script type="text/javascript" src="{{ asset('assets/js/pages/bulk/template.js') }}"></script>

    <script>
        $(document).ready(function() {
            document.getElementById('msg_template').innerText = null;



            // $("#device_id").change(function() {
            // var selectedDevice = $(this).val();
            var selectedDevice = $('#device_id').val();
            // console.log('selectedDevice is ', selectedDevice);
            $('#msg_template').empty().append(
                '<option value="" >-- Select Template --</option>');

            if (!selectedDevice) return;

            $.ajax({
                url: "{{ route('user.get_templates', ':device_id') }}".replace(':device_id',
                    selectedDevice),
                type: 'GET',
                success: function(templates) {
                    $.each(templates, function(index, value) {
                        if (value.status == "1") {
                            $('#msg_template').append("<option value='" + value
                                .id +
                                "'>" + value.title + " -- " + value.type +
                                "</option>");
                        }
                    });

                    // After loading templates, check if there's existing integration data
                    var indIntegration = @json($im_templates);
                    if (indIntegration && indIntegration.msg_template) {
                        // Pre-select the existing template
                        $('#msg_template').val(indIntegration.msg_template);

                        // Pre-fill the IndiaMART key
                        if (indIntegration.im_key) {
                            $('#im_key').val(indIntegration.im_key);
                        }

                        // Show delete button
                        $('#delete_im_integration').show();
                    }
                },
                error: function(xhr) {
                    console.error('AJAX error:', xhr.responseText);
                }
            });

            // });

            // Also check for existing integration data on page load (fallback)
            var indIntegration = @json($im_templates);
            if (indIntegration && indIntegration.im_key) {
                // Pre-fill the IndiaMART key immediately
                $('#im_key').val(indIntegration.im_key);

                // Show delete button if integration exists
                $('#delete_im_integration').show();
            }
        });
    </script>
@endpush
