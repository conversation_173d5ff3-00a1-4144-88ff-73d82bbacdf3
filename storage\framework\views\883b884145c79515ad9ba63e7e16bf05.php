
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', ['title' => __('Edit Drip Campaign')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/select2/dist/css/select2.min.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12 col-sm-12 col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h4><?php echo e(__('Edit Drip Campaign')); ?></h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 col-sm-12 col-md-12">
                            <div class="tab-content no-padding" id="myTab2Content">
                                <div class="tab-pane fade show active" id="home4" role="tabpanel"
                                    aria-labelledby="home-tab4">
                                    <form method="POST"
                                        action="<?php echo e(route('user.drip_campaign.update', $drip_campaign->id)); ?>"
                                        class="ajaxform">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('PUT'); ?>
                                        <div class="row">
                                            <div class="form-group col-md-6">
                                                <!-- Drip Name field -->
                                                <label for="drip_name"><?php echo e(__('Drip Name')); ?></label>
                                                <input type="text" class="form-control" name="drip_name" id="drip_name"
                                                    value="<?php echo e($drip_campaign->drip_name); ?>" required>
                                            </div>
                                            <div class="form-group col-md-6">
                                                <!-- Select Device Dropdown field -->
                                                <label for="device_name"><?php echo e(__('Select Device')); ?></label>
                                                <select class="form-control" id="device_id" name="device_id" required
                                                    data-toggle="select">
                                                    <option value=""><?php echo e(__('-- Select Device --')); ?></option>
                                                    <?php $__currentLoopData = $devices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($device['id']); ?>"
                                                            <?php if($drip_campaign->device_id == $device['id']): ?> selected <?php endif; ?>>
                                                            <?php echo e($device['name']); ?>(+<?php echo e($device['phone']); ?>)
                                                        </option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                        <hr>

                                        <div id="dripFieldsContainer">
                                            <?php $__currentLoopData = $drip_campaign->drip_data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $drip): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="row drip-row"
                                                    style="border:1px solid #ccc; padding: 5px; margin: 5px;border-radius: 5px;">
                                                    <div class="form-group col-md-2">
                                                        <label for="drip_count"><?php echo e(__('Drip')); ?></label>
                                                        <input type="text" class="form-control drip-count-input"
                                                            name="drip_count[]" value="<?php echo e($drip['drip_count']); ?>" readonly>
                                                    </div>
                                                    <div class="form-group col-md-1">
                                                        <label for="days"><?php echo e(__('Days')); ?></label>
                                                        <input type="text" class="form-control" name="days[]"
                                                            value="<?php echo e($drip['days']); ?>" min="1" max="60"
                                                            required>
                                                    </div>
                                                    <div class="form-group col-md-4">
                                                        <label for="drip_template"><?php echo e(__('Template')); ?></label>
                                                        <select class="form-control template-select" name="drip_template[]"
                                                            required data-toggle="select" data-current="<?php echo e($drip['template']); ?>">
                                                            <option value=""><?php echo e(__('-- Select Template --')); ?>

                                                            </option>
                                                        </select>
                                                    </div>
                                                    <?php if($drip['media_path']): ?>
                                                        <div class="form-group col-md-2">
                                                            <label>&nbsp;</label>
                                                            
                                                            <a href="<?php echo e(asset($drip['media_path'])); ?>"
                                                                target="_blank"><?php echo e(__('View Media')); ?></a>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div class="form-group col-md-1">
                                                        <label>&nbsp;</label>
                                                        <div>
                                                            <!-- Button will be dynamically updated by JavaScript -->
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                        <div class="form-group">
                                            <button
                                                class="btn btn-neutral submit-button float-right"><?php echo e(__('Update')); ?></button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/vendor/select2/dist/js/select2.min.js')); ?>"></script>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function() {
            const MAX_DRIPS = 5;

            // Initial setup for existing rows
            initializeExistingRows();
            updateDripCounts();
            updateRowButtons();

            function initializeExistingRows() {
                // Load templates for each existing row
                $('.drip-row').each(function(index) {
                    const $row = $(this);
                    const deviceId = $('#device_id').val();
                    const currentTemplate = $row.find('select[name="drip_template[]"]').data('current');

                    // Replace existing buttons with dynamic buttons
                    updateRowActionButtons($row, index);

                    // Populate template dropdown and select current value
                    populateTemplateDropdown($row, deviceId, currentTemplate);
                    // initializeTemplateHandler($row);
                });
            }

            function updateRowActionButtons($row, index) {
                const $buttonContainer = $row.find('.form-group:last-child div');
                $buttonContainer.empty();

                if (index === 0) {
                    // First row: Show only add button if under max limit
                    if ($('.drip-row').length < MAX_DRIPS) {
                        $buttonContainer.append(`
                            <button type="button" class="btn btn-neutral btn-md add_button">
                                <i class="fa fa-plus"></i>
                            </button>
                        `);
                    }
                } else {
                    // Other rows: Show remove button
                    $buttonContainer.append(`
                        <button type="button" class="btn btn-danger btn-md remove-row">
                            <i class="fa fa-minus"></i>
                        </button>
                    `);
                }
            }

            function updateRowButtons() {
                $('.drip-row').each(function(index) {
                    updateRowActionButtons($(this), index);
                });
            }

            // Handle device change
            $("#device_id").change(function() {
                const deviceId = $(this).val();

                if (!deviceId) {
                    // Clear all template dropdowns if no device selected
                    $('.template-select').empty().append('<option value="">-- Select Template --</option>');
                    return;
                }

                // Update template dropdowns for all rows
                $('.drip-row').each(function() {
                    populateTemplateDropdown($(this), deviceId);
                });
            });

            // Add new row
            $(document).on('click', '.add_button', function() {
                if ($('.drip-row').length >= MAX_DRIPS) {
                    alert('Maximum ' + MAX_DRIPS + ' drips allowed');
                    return;
                }

                const nextDripNumber = $('.drip-row').length + 1;
                const deviceId = $("#device_id").val();

                const newRow = `
                    <div class="row drip-row" style="border:1px solid #ccc; padding: 5px; margin: 5px;border-radius: 5px;">
                        <div class="form-group col-md-2">
                            <label><?php echo e(__('Drip')); ?></label>
                            <input type="text" class="form-control drip-count-input" name="drip_count[]" value="Drip ${nextDripNumber}" readonly>
                        </div>
                        <div class="form-group col-md-1">
                            <label><?php echo e(__('Days')); ?></label>
                            <input type="text" class="form-control" name="days[]" placeholder="Days" min="1" max="60" required>
                        </div>
                        <div class="form-group col-md-4">
                            <label><?php echo e(__('Template')); ?></label>
                            <select class="form-control template-select" name="drip_template[]" data-toggle="select" required>
                                <option value=""><?php echo e(__('-- Select Template --')); ?></option>
                            </select>
                        </div>
                        <div class="form-group col-md-1">
                            <label>&nbsp;</label>
                            <div></div>
                        </div>
                    </div>
                `;

                $('#dripFieldsContainer').append(newRow);
                const $newRow = $('.drip-row:last');

                populateTemplateDropdown($newRow, deviceId);
                // initializeTemplateHandler($newRow);
                updateRowButtons();
            });

            // Remove row
            $(document).on('click', '.remove-row', function() {
                $(this).closest('.drip-row').remove();
                updateDripCounts();
                updateRowButtons();
            });

            function populateTemplateDropdown($row, deviceId, currentTemplate = null) {
                const $templateSelect = $row.find('.template-select');

                // If currentTemplate is not provided, get it from data attribute
                if (!currentTemplate) {
                    currentTemplate = $templateSelect.data('current');
                }

                $templateSelect.empty().append('<option value="">-- Select Template --</option>');

                if (!deviceId) return;

                $.ajax({
                    url: "<?php echo e(route('user.get_templates', ':device_id')); ?>".replace(':device_id', deviceId),
                    type: 'GET',
                    success: function(templates) {
                        $.each(templates, function(index, value) {
                            if (value.status == "1") {
                                // Compare with template ID (stored value) instead of name
                                const selected = currentTemplate && value.id ==
                                    currentTemplate ? 'selected' : '';
                                $templateSelect.append(`
                                    <option value="${value.id}" ${selected}>
                                        ${value.title} -- ${value.type}
                                    </option>
                                `);
                            }
                        });
                    },
                    error: function(xhr) {
                        console.error('AJAX error:', xhr.responseText);
                    }
                });
            }

            function initializeTemplateHandler($row) {
                $row.find('.template-select').on('change', function() {
                    const deviceId = $("#device_id").val();
                    const templateName = $(this).val();
                    const deviceData = [];

                    if (!deviceId || !templateName) return;

                    const template = deviceData[deviceId].templates.find(t => t.name === templateName);
                    if (!template) return;

                    updateRowSettings($row, template);
                });

                // Trigger change event for existing selections
                const selectedTemplate = $row.find('.template-select').val();
                if (selectedTemplate) {
                    $row.find('.template-select').trigger('change');
                }
            }

            function updateRowSettings($row, template) {
                const $mediaSection = $row.find('.media-section');
                const $mediaFile = $row.find('.media-file');
                const $hasMediaInput = $row.find('.has-media-input');
                const $taskTypeInput = $row.find('.task-type-input');
                const $languageInput = $row.find('.language-input');

                // Set language
                $languageInput.val(template.language);

                // Handle media settings based on template format
                const format = template.components[0].format;
                const mediaSettings = {
                    'IMAGE': {
                        showMedia: true,
                        hasMedia: '1',
                        taskType: '2'
                    },
                    'DOCUMENT': {
                        showMedia: true,
                        hasMedia: '1',
                        taskType: '3'
                    },
                    'VIDEO': {
                        showMedia: true,
                        hasMedia: '1',
                        taskType: '4'
                    },
                    'TEXT': {
                        showMedia: false,
                        hasMedia: '0',
                        taskType: '1'
                    }
                };

                const settings = mediaSettings[format] || mediaSettings['TEXT'];

                $mediaSection.toggle(settings.showMedia);
                $mediaFile.prop('disabled', !settings.showMedia);
                $hasMediaInput.val(settings.hasMedia);
                $taskTypeInput.val(settings.taskType);
            }

            function updateDripCounts() {
                $('.drip-row').each(function(index) {
                    $(this).find('.drip-count-input').val('Drip ' + (index + 1));
                });
            }

            function updateAddButtonVisibility() {
                $('.add_button').toggle($('.drip-row').length < MAX_DRIPS);
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/drip_campaign/edit.blade.php ENDPATH**/ ?>